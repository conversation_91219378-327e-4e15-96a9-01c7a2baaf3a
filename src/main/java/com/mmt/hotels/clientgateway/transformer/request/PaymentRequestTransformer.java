package com.mmt.hotels.clientgateway.transformer.request;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ValidationErrors;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PaymentHelper;
import com.mmt.hotels.clientgateway.request.payment.PaymentRequestClient;
import com.mmt.hotels.clientgateway.request.payment.SpecialRequestCategory;
import com.mmt.hotels.clientgateway.request.payment.TravellerDetail;
import com.mmt.hotels.clientgateway.util.HeadersUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.AddOnDetailState;
import com.mmt.hotels.model.request.payment.*;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import com.mmt.hotels.model.response.pricing.SpecialRequest;
import com.mmt.scrambler.exception.ScramblerClientException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.USER_CURRENCY;

@Component
public class PaymentRequestTransformer {

    @Autowired
    PaymentHelper payHelper;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    private Utility utility;

    public BeginCheckoutReqBody modifyPaymentRequest(PaymentRequestClient paymentRequest, HttpServletRequest httpServletRequest, String client)throws ScramblerClientException , ClientGatewayException{
        BeginCheckoutReqBody beginCheckoutReqBody = new BeginCheckoutReqBody();
        BeanUtils.copyProperties(paymentRequest,beginCheckoutReqBody);
        paymentRequest.setIdContext(commonHelper.updateIdContext(paymentRequest.getIdContext(), client));
        populateSiteDomainProperties(beginCheckoutReqBody);
        populatePaymentDetail(paymentRequest,beginCheckoutReqBody);
        populateAuthenticationDetail(paymentRequest,beginCheckoutReqBody);
        populateTravellerDetail(paymentRequest,beginCheckoutReqBody);
        populateSpecialRequest(paymentRequest,beginCheckoutReqBody);
        populatetOtherDetails(paymentRequest,beginCheckoutReqBody);
        populateWorkflowData(paymentRequest, beginCheckoutReqBody);
        populateAddressDetails(paymentRequest, beginCheckoutReqBody);
        beginCheckoutReqBody.setRequestIdentifier(utility.buildRequestIdentifier(paymentRequest.getRequestDetails()));
        beginCheckoutReqBody.setFlexibleCheckinSlotId(paymentRequest.getFlexibleCheckinSlotId());
        beginCheckoutReqBody.setUserConsent(paymentRequest.isPromoConsent());
        beginCheckoutReqBody.setDigiLockerConsent(paymentRequest.isDigiLockerConsent());
        beginCheckoutReqBody.setAddOnDetailUpsell(buildAddOnDetails(paymentRequest.getAddOnDetail()));
        beginCheckoutReqBody.setExternalChainMembershipID(paymentRequest.getExternalChainMembershipID());
        Map<String, String> headerMap = HeadersUtil.getHeadersFromServletRequest(httpServletRequest);
        //Setting Header's user-currency to request-body currency
        if (StringUtils.isNotEmpty(headerMap.get(Constants.USER_CURRENCY))) {
            beginCheckoutReqBody.setCurrency(headerMap.get(Constants.USER_CURRENCY).toUpperCase());
        }
        //Setting default currency basis region for specific App versions
        if (StringUtils.isNotEmpty(headerMap.get("ver")) && utility.checkAppVersionForCurrency(client, headerMap.get("ver"))) {
            if (StringUtils.isEmpty(headerMap.get(Constants.USER_CURRENCY)) && headerMap.containsKey(Constants.CURRENCY)) {
                beginCheckoutReqBody.setCurrency(headerMap.get(Constants.CURRENCY));
            }
        }
        // Setting review page omniture events data received from client for myPartner
        if (null != paymentRequest.getReviewPageOmni()) {
            beginCheckoutReqBody.setOmnitureEventDataMmt(paymentRequest.getReviewPageOmni());
        }
        return payHelper.modifyPaymentRequest(beginCheckoutReqBody,httpServletRequest);
    }

    private Map<String, AddOnDetailState> buildAddOnDetails(Map<String,com.mmt.hotels.clientgateway.request.AddOnDetailState> addOnDetail) {
        Map<String, AddOnDetailState> addOnDetailCB = null;
        if (MapUtils.isNotEmpty(addOnDetail)) {
            addOnDetailCB = new HashMap<>();
            for (Map.Entry<String, com.mmt.hotels.clientgateway.request.AddOnDetailState> entry : addOnDetail.entrySet()) {
                AddOnDetailState addOnDetailState = new AddOnDetailState();
                addOnDetailState.setAddOnType(entry.getValue().getAddOnType());
                addOnDetailState.setSelected(com.mmt.hotels.model.request.AddOnState.valueOf(entry.getValue().getSelected().name()));
                addOnDetailCB.put(entry.getKey(), addOnDetailState);
            }
        }
        return addOnDetailCB;
    }

    /**
     *  For logged-out users, address details will be coming from the client in request body in gstnDetails key.
     *  For logged-in users, address details will be coming from the client in request body if it's not already added in profile section.
     *  Populate address detail in HES request body if coming from client. ( If this information is available from user service, then this info is picked from that response )
     *
     * @param paymentRequest is the payment-checkout body coming from client
     * @param beginCheckoutReqBody is the body for HES request
     */
    private void populateAddressDetails(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody) throws ClientGatewayException {
        AddressDetails addressDetails = null;
        // HTL-43809-  when state will come under traveller details for myBiz we will send it to booker
        if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(paymentRequest.getIdContext())) {
            addressDetails = new AddressDetails();
            String state = Optional.ofNullable(paymentRequest.getTravellerDetails()
                            .stream()
                            .findFirst()
                            .orElse(null))
                    .map(TravellerDetail::getState).orElse(Constants.EMPTY_STRING);
            addressDetails.setState(state);
        } else if (paymentRequest.getGstnDetail() != null) { // when state is comiing under gstnDetail node for B2C we sending it to booker.
            addressDetails = new AddressDetails();
            addressDetails.setAddress1(paymentRequest.getGstnDetail().getAddress());
            addressDetails.setPostalCode(paymentRequest.getGstnDetail().getPinCode());
            addressDetails.setState(paymentRequest.getGstnDetail().getState());
        }
        beginCheckoutReqBody.setAddressDetails(addressDetails);

    }

    public BeginCheckoutReqBody modifyPaymentRequest(BeginCheckoutReqBody paymentRequest, HttpServletRequest httpServletRequest)throws ScramblerClientException , ClientGatewayException{
        return payHelper.modifyPaymentRequest(paymentRequest,httpServletRequest);
    }

    private void populateWorkflowData(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody) {

        if (!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(paymentRequest.getIdContext())){
            return;
        }

        beginCheckoutReqBody.setWorkflowData(new WorkflowData());
        beginCheckoutReqBody.getWorkflowData().setInitiateApprovalWorkflowRequest(new WorkflowRequest());
        beginCheckoutReqBody.getWorkflowData().setWorkflowStatus(paymentRequest.getWorkflowStatus());
        if (paymentRequest.getTripTag()!=null){

            String tripTag = new Gson().toJson(paymentRequest.getTripTag());
            beginCheckoutReqBody.getWorkflowData().setInitiateApprovalWorkflowRequest(new WorkflowRequest());
            beginCheckoutReqBody.getWorkflowData().getInitiateApprovalWorkflowRequest().setTripTag(new TripTag());
            beginCheckoutReqBody.getWorkflowData().getInitiateApprovalWorkflowRequest().setTripTag(new Gson().fromJson(tripTag,TripTag.class));
        }

    }



    private void populateSiteDomainProperties(BeginCheckoutReqBody paymentRequest){
        paymentRequest.setSiteDomain(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
        paymentRequest.setDomainCountry(paymentRequest.getSiteDomain());
        paymentRequest.setDomainLanguage(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()));
    }

    private void populatetOtherDetails(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody){
        if(paymentRequest.getErrorConfig() != null) {
            beginCheckoutReqBody.setErrorConfig(new ErrorConfig());
            beginCheckoutReqBody.getErrorConfig().setSessionTimeoutURL(paymentRequest.getErrorConfig().getSessionTimeoutURL());
        }

        if(paymentRequest.getAdditionalCheckoutInfo() != null){
            beginCheckoutReqBody.setAdditionalCheckoutInfo(new AdditionalCheckoutInfo());
            BeanUtils.copyProperties(paymentRequest.getAdditionalCheckoutInfo(), beginCheckoutReqBody.getAdditionalCheckoutInfo());
        }
        beginCheckoutReqBody.setPersonalCorpBooking(paymentRequest.isPersonalCorpBooking());
        beginCheckoutReqBody.setReasonForSkipApproval(paymentRequest.getReasonForSkipApproval());
        // myBizWalletQuickPay is true when eligible users who can opt to pay through wallet without requesting approval is doing so
        beginCheckoutReqBody.setMyBizWalletQuickPay(paymentRequest.isMyBizWalletQuickPay());
    }

    private void populateSpecialRequest(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody){
        if(paymentRequest.getSpecialRequest() != null && CollectionUtils.isNotEmpty(paymentRequest.getSpecialRequest().getCategories()) ){
            beginCheckoutReqBody.setSpecialRequest(new SpecialRequest());
            beginCheckoutReqBody.getSpecialRequest().setDisclaimer(paymentRequest.getSpecialRequest().getDisclaimer());
            beginCheckoutReqBody.getSpecialRequest().setCategories(new ArrayList<>());
            for(SpecialRequestCategory category : paymentRequest.getSpecialRequest().getCategories()){
                com.mmt.hotels.model.response.pricing.SpecialRequestCategory sRC = new com.mmt.hotels.model.response.pricing.SpecialRequestCategory();
                BeanUtils.copyProperties(category, sRC);
                if(CollectionUtils.isNotEmpty(category.getSubCategories())) {
                    sRC.setSubCategories(new ArrayList<>());
                    for (SpecialRequestCategory subCat : category.getSubCategories()) {
                        com.mmt.hotels.model.response.pricing.SpecialRequestCategory sRCSub = new com.mmt.hotels.model.response.pricing.SpecialRequestCategory();
                        BeanUtils.copyProperties(subCat,sRCSub);
                        sRC.getSubCategories().add(sRCSub);
                    }
                }
                beginCheckoutReqBody.getSpecialRequest().getCategories().add(sRC);
            }
        }
    }

    private void populatePaymentDetail(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody){
        beginCheckoutReqBody.setPaymentDetail(new PaymentDetail());
        BeanUtils.copyProperties(paymentRequest.getPaymentDetail() , beginCheckoutReqBody.getPaymentDetail());
        if(paymentRequest.getPaymentDetail().getEmiDetails() != null) {
            beginCheckoutReqBody.getPaymentDetail().setEmiDetails(new EmiDetailsRequest());
            BeanUtils.copyProperties(paymentRequest.getPaymentDetail().getEmiDetails(), beginCheckoutReqBody.getPaymentDetail().getEmiDetails());
        }
        if(paymentRequest.getPaymentDetail() != null) {
            beginCheckoutReqBody.getPaymentDetail().setIsBNPL(paymentRequest.getPaymentDetail().isBNPL());
            beginCheckoutReqBody.getPaymentDetail().setPartialPayment(paymentRequest.getPaymentDetail().isPartialPayment());
            beginCheckoutReqBody.getPaymentDetail().setBookNowFareHold(paymentRequest.getPaymentDetail().isBookNowFareHold());
        }

    }

    private void populateAuthenticationDetail(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody){
        if(paymentRequest.getAuthenticationDetail() != null && paymentRequest.getAuthenticationDetail().getOtpDetail() != null) {
            beginCheckoutReqBody.setAuthenticationDetail(new AuthenticationDetail());
            beginCheckoutReqBody.getAuthenticationDetail().setOtpDetail(new OTPDetail());
            beginCheckoutReqBody.getAuthenticationDetail().getOtpDetail().setKey(paymentRequest.getAuthenticationDetail().getOtpDetail().getKey());
            beginCheckoutReqBody.getAuthenticationDetail().getOtpDetail().setOtp(paymentRequest.getAuthenticationDetail().getOtpDetail().getOtp());
        }
    }

    private void populateTravellerDetail(PaymentRequestClient paymentRequest, BeginCheckoutReqBody beginCheckoutReqBody){
        List<TravelerDetail> trvlrList = new ArrayList<>();
        for(com.mmt.hotels.clientgateway.request.payment.TravellerDetail trvlr : paymentRequest.getTravellerDetails()){
            TravelerDetail newTrvlr = new TravelerDetail();
            BeanUtils.copyProperties(trvlr,newTrvlr);
            trvlrList.add(newTrvlr);
        }
        beginCheckoutReqBody.setTravelerDetailsList(trvlrList);
    }


}
