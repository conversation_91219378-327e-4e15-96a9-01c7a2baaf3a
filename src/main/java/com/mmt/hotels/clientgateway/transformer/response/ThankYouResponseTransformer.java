package com.mmt.hotels.clientgateway.transformer.response;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.MyTripActionUrls;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FlexiCancelStaticDetail;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.payment.InsuranceInfo;
import com.mmt.hotels.clientgateway.response.Cta;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.PropertyRules;
import com.mmt.hotels.clientgateway.response.dayuse.DayUseDetails;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardPayloadData;
import com.mmt.hotels.clientgateway.response.staticdetail.CategoryTag;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.thankyou.BookerInfo;
import com.mmt.hotels.clientgateway.response.thankyou.*;
import com.mmt.hotels.clientgateway.response.thankyou.uiData.PaymentInfo;
import com.mmt.hotels.clientgateway.response.thankyou.uiData.*;
import com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.addon.AddOnType;
import com.mmt.hotels.model.request.payment.AddOnDetail;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.persuasion.OnlyForTodayPersuasionDetails;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.txn.*;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.mmt.hotels.clientgateway.constants.ClientExpDataKeys.CLIENT_EXP_Thankyou_PAGE_REDESIGN;
import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.FREE_CANCELLATION;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

public abstract class ThankYouResponseTransformer {

    private static final Gson gson = new Gson();

    private Map<String, String> mealPlanMapPolyglot;

    @Value("${thankyou.max.inclusions}")
    private int maxInclusionsThankyou;

    @Value("${thankyou.mytrips.section.actions.config}")
    private String myTripCardDetails;

    @Value("${thankyou.mytrips.section.actions.config.v2}")
    private String myTripCardDetailsV2;

    @Value("${thankyou.mytrips.section.actions.cta.text.replacer}")
    private String myTripCardCtaTextReplacer;

    @Value("${thankyou.mytrips.section.actions.icon.urls}")
    private String myTripCardIconUrls;

    @Value("${thankyou.mytrips.section.conditions}")
    private String myTripCardConditions;

    @Value("${thankyou.pancard.icon.url}")
    private String panCardIconUrl;

    @Value("${forex.deeplink.url.apps}")
    private String forexDeeplinkUrlApps;

    @Value("${forex.deeplink.url.dtPwa}")
    private String forexDeeplinkUrlDtPwa;

    @Value("${cabs.forex.deeplink.url.apps}")
    private String cabsforexDeeplinkUrlApps;

    @Value("${cabs.forex.deeplink.url.dtPwa}")
    private String cabsforexDeeplinkUrlDtPwa;

    @Value("#{'${flights.booker.hydraSegments}'.split(',')}")
    private Set<String> flightsBookerHydraSegment;

    @Value("${forex.icon.url}")
    private String forexIconUrl;

    @Value("${cab.cashback.icon.url}")
    private String cabCashbackIconUrl;

    @Value("${cab.web.view.url}")
    private String cabWebViewUrl;

    @Value("${forex.cashback.icon.url}")
    private String forexCashbackIconUrl;

    @Value("${forex.web.view.url}")
    private String forexWebViewUrl;

    @Value("${forex.promoIcon.url}")
    private String forexPromoIconUrl;

    @Value("${digilocker.card.icon.url}")
    private String digiLockerCardIconUrl;

    @Value("#{'${flyer.Hydra.SegmentId.list.mmt.intl}'.split(',')}")
    private Set<String> intlFlyerHydraSegmentIds;

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Value("${gcc.lpg.card.payload}")
    private String gccLpgCardData;

    @Value("${mypartner.gst.assured.icon.url}")
    private String iconUrlGSTAssured;

    @Value("${onlytodaydeal.icon.url}")
    private String onlyTodayDealiconUrl;

    @Value("${header.icon.success}")
    private String headerIconSuccess;
    @Value("${header.icon.failed}")
    private String headerIconFailed;
    @Value("${header.icon.pending}")
    private String headerIconPending;
    @Value("${inclusion.icon.red.info}")
    private String inclusionIconRedInfo;
    @Value("${inclusion.icon.single.tick.grey}")
    private String inclusionIconSingleTickGrey;
    @Value("#{'${pixel.tracking.locations}'.split(',')}")
    private Set<String> pixelTrackingLocations;


    @Autowired
    CommonConfigConsul commonConfigConsul;
    @Autowired
    PersuasionUtil persuasionUtil;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    private Map<String, MyTripCard> myTripsCardTypeToCardDetails;

    private Map<String, String> myTripsCtaTextReplacer;

    private Map<String, MyTripCard> myTripsCardTypeToCardDetailsModified;

    private Map<String, MyTripCard> myTripsCardTypeToCardDetailsV2;

    protected Map<String, MyTripActionUrls> myTripsCardTypeToIconUrls;

    private Map<String, List<String>> myTripsConditionsToCardsList;

    private Map<String, CardData> thankYouCards;

    private CardInfo lgpWelcomeCardData;

    @Value("#{'${group.booking.thank.you.page.cards}'.split(',')}")
    private List<String> groupBookingCardKeys;

    List<String> sameDayRoomNames;

    private static final Logger logger = LoggerFactory.getLogger(ThankYouResponseTransformer.class);

    @Autowired
    PropertyManager propManager;

    @Autowired
    private  Utility utility;

    @Autowired
    protected PolyglotService polyglotService;

    @Autowired
    protected PolyglotHelper polyglotHelper;

    @Value("${super.package.icon.url}")
    private String superPackageIconUrl;

    @Value("${myBiz.gst.invoices.icon.url}")
    private String myBizGstInvoicesIconUrl;

    private FlexiCancelStaticDetail flexiCancelStaticDetail;

    protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);

    @PostConstruct
    public void init() {
        Gson gson = new Gson();
        myTripsCardTypeToCardDetails = gson.fromJson(myTripCardDetails, new TypeToken<Map<String, MyTripCard>>() {
        }.getType());
        myTripsCardTypeToCardDetailsV2 = gson.fromJson(myTripCardDetailsV2, new TypeToken<Map<String, MyTripCard>>() {
        }.getType());
        myTripsCtaTextReplacer = gson.fromJson(myTripCardCtaTextReplacer, new TypeToken<Map<String, String>>() {
        }.getType());

        myTripsConditionsToCardsList = gson.fromJson(myTripCardConditions, new TypeToken<Map<String, List<String>>>() {
        }.getType());

        myTripsCardTypeToIconUrls = gson.fromJson(myTripCardIconUrls, new TypeToken<Map<String, MyTripActionUrls>>() {
        }.getType());

        try {
            if(consulFlag){
                mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
                thankYouCards = commonConfigConsul.getThankYouCards();
                sameDayRoomNames = commonConfigConsul.getSameDayRoomNames();
                flexiCancelStaticDetail = commonConfigConsul.getFlexiCancelStaticDetail();
                lgpWelcomeCardData = commonConfigConsul.getLgpWelcomeCardData();
                logger.debug("Fetching values from commonConfig consul");
            }
            else{
                CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
                mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
                thankYouCards = commonConfig.thankYouCards();
                sameDayRoomNames = commonConfig.sameDayRoomNames();
                commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
                commonConfig.addPropertyChangeListener("thankYouCards", evt -> thankYouCards = commonConfig.thankYouCards());
                commonConfig.addPropertyChangeListener("sameDayRoomNames", evt -> sameDayRoomNames = commonConfig.sameDayRoomNames());
            }
        }catch (Exception ex){
            logger.error("error in fetching meal planmap");
        }
    }

    private HotelCloudCallOutData buildHotelCloudCallOutData() {
        HotelCloudCallOutData hotelCloudCallOutData = new HotelCloudCallOutData();
        hotelCloudCallOutData.setTitle(polyglotService.getTranslatedData(AVOID_HASSLE_COLLECTING_GST_INVOICES));
        hotelCloudCallOutData.setImageUrl(myBizGstInvoicesIconUrl);
        hotelCloudCallOutData.setDescription(polyglotService.getTranslatedData(HOTEL_CLOUD_GST_DESCRIPTION));
        hotelCloudCallOutData.setFooterText(polyglotService.getTranslatedData(POWERED_BY_HOTEL_CLOUD));
        hotelCloudCallOutData.setBgLinearGradient(utility.buildBgLinearGradientForHotelCloud());

        return hotelCloudCallOutData;
    }

    public ThankYouResponse convertThankYouResponse(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity, FeatureFlags featureFlags, Map<String, String> parameterMap) throws ErrorResponseFromDownstreamException {
        if (persistanceMultiRoomResponseEntity == null || persistanceMultiRoomResponseEntity.getPersistedData() == null)
            throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM);

        Map<String, String> expDataMap = persistanceMultiRoomResponseEntity!= null && persistanceMultiRoomResponseEntity.getPersistedData() != null ? persistanceMultiRoomResponseEntity.getPersistedData().getExpData() : new HashMap<>();
        boolean isThankyouV2 = isThankyouV2(expDataMap);
        boolean specialRequestV2 = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.SPECIAL_REQUEST.getKey()) && Boolean.parseBoolean(expDataMap.get(ExperimentKeys.SPECIAL_REQUEST.getKey()));

        ThankYouResponse thankYouResponse = new ThankYouResponse();
        AmountDetail amountDetail = buildTotalAmount(persistanceMultiRoomResponseEntity.getPersistedData());
        thankYouResponse.setBookingDetails(buildBookingDetails(persistanceMultiRoomResponseEntity.getPersistedData(), amountDetail, isThankyouV2));
        thankYouResponse.setTotalAmount(amountDetail);


        if (thankYouResponse.getTotalAmount() == null) { //full amount not paid.. i.e bnpl or pah cases
            populateAmountBreakup(thankYouResponse, persistanceMultiRoomResponseEntity);
        }

        if (thankYouResponse.getPaidAmount() == null) {
            if(persistanceMultiRoomResponseEntity.getPersistedData().isPartialPayment()) {
                thankYouResponse.setPaidAmount(buildPartialAmountPaid(persistanceMultiRoomResponseEntity.getPersistedData()));
            } else {
                thankYouResponse.setPaidAmount(thankYouResponse.getTotalAmount());//for PAS case both are same
            }
            updatePgChargesInPaidAmount(thankYouResponse.getPaidAmount(), persistanceMultiRoomResponseEntity.getPersistedData());
        }
        thankYouResponse.setHotelDetails(buildHotelDetails(persistanceMultiRoomResponseEntity.getPersistedData()));
        if(persistanceMultiRoomResponseEntity!=null && persistanceMultiRoomResponseEntity.getPersistedData()!=null && persistanceMultiRoomResponseEntity.getPersistedData().isPackageRate()) {
            thankYouResponse.setResponseTrackingKey(PACKAGE_RATE_TRACKING_TEXT);
        }
        if(persistanceMultiRoomResponseEntity!=null && persistanceMultiRoomResponseEntity.getPersistedData()!=null && StringUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedType()) && MEAL_UPGRADE.equalsIgnoreCase(persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedType())) {
            thankYouResponse.setResponseTrackingKey(MEAL_UPSELL_TRACKING_TEXT);
        }
        if(persistanceMultiRoomResponseEntity!=null && persistanceMultiRoomResponseEntity.getPersistedData()!=null && StringUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedType())) {
            thankYouResponse.setResponseTrackingKey(persistanceMultiRoomResponseEntity.getPersistedData().getRecommendedType());
        }
        if(persistanceMultiRoomResponseEntity!=null && persistanceMultiRoomResponseEntity.getPersistedData()!=null && persistanceMultiRoomResponseEntity.getPersistedData().isBusinessIdentificationApplicable()) {
            thankYouResponse.setResponseTrackingKey(B2C_BUSINESS_HOTELS_TRACKING_TEXT);
        }
        if(persistanceMultiRoomResponseEntity!=null && persistanceMultiRoomResponseEntity.getPersistedData() != null && persistanceMultiRoomResponseEntity.getPersistedData().getOnlyForTodayPersuasionDetails() != null) {
            if(persistanceMultiRoomResponseEntity.getPersistedData().getOnlyForTodayPersuasionDetails().isEnabled() == true){
                thankYouResponse.setOnlyTodayDealInfo(setOnlyTodayDealInfo(persistanceMultiRoomResponseEntity.getPersistedData().getOnlyForTodayPersuasionDetails()));
                buildTrackingMap(thankYouResponse, "m_c8", ONLY_TODAY_DEAL_THANK_YOU_TRACKING_TEXT);
            }
        }
        thankYouResponse.setTravellers(buildTravellersDetails(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setRooms(buildBookedRooms(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse, expDataMap));
        if (persistanceMultiRoomResponseEntity.getPersistedData().getFlexibleCheckIn() != null) {
            thankYouResponse.setFlexibleCheckinInfo(persistanceMultiRoomResponseEntity.getPersistedData().getFlexibleCheckIn());
        }
        thankYouResponse.setPropertyRules(buildPropertyRules(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse));
        if(specialRequestV2) {
            thankYouResponse.setSelectedSpecialRequestsV2(buildSelectedSpecialRequestsV2(persistanceMultiRoomResponseEntity.getPersistedData()));
        } else {
            thankYouResponse.setSelectedSpecialRequests(buildSelectedSpecialRequests(persistanceMultiRoomResponseEntity.getPersistedData()));
        }
        thankYouResponse.setMyTripsSection(buildMyTripsSection(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setExperimentData(buildExperimentData(persistanceMultiRoomResponseEntity.getPersistedData()));
        thankYouResponse.setUserLoyaltyStatus(persistanceMultiRoomResponseEntity.getPersistedData().getUserLoyaltyStatus());
        thankYouResponse.setTrafficSource(persistanceMultiRoomResponseEntity.getPersistedData().getAvailReqBody().getTrafficSource());
        if (thankYouResponse.getHotelDetails() != null){
            thankYouResponse.setSafetyPersuasionMap(commonResponseTransformer.buildSafetyPersuasionList(thankYouResponse.getHotelDetails().getCategories()));
            if("android".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || "ios".equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                thankYouResponse.getHotelDetails().setApplicableHotelCategoryData(commonResponseTransformer.buildHotelCategoryDataMap(thankYouResponse.getHotelDetails().getCategories(), false));
            }
            else {
                thankYouResponse.getHotelDetails().setApplicableHotelCategoryDataWeb(commonResponseTransformer.buildHotelCategoryDataWeb(thankYouResponse.getHotelDetails().getCategories()));
            }
        }
        updateInclusions(thankYouResponse, featureFlags != null && featureFlags.isAllInclusions());
        buildAdditionalCharges(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildCardDetails(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildLogData(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        buildMetaChannelInfo(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildRtbChatCard(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildGSTDetails(persistanceMultiRoomResponseEntity.getPersistedData(), thankYouResponse);
        buildPgCharges(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildBPGText(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        if(persistanceMultiRoomResponseEntity.getPersistedData().isPartialPayment()) {
            thankYouResponse.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(persistanceMultiRoomResponseEntity.getPersistedData().getPaymentPlan()));
        }
        buildPendingAmount(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildMyPatHeroBanner(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        buildMyPartnerFareHoldData(thankYouResponse,persistanceMultiRoomResponseEntity.getPersistedData());
        /*build cashback offer persuasion/Hero offer persuasion for thankyou page myPartner funnel*/
        thankYouResponse.setHotelPersuasions(buildPersuasionsMap(persistanceMultiRoomResponseEntity.getPersistedData()));
        if (utility.isMyPartnerExclusiveDealAllowed(persistanceMultiRoomResponseEntity.getPersistedData())) {
            persuasionUtil.buildHotelPersuasionOfExclusiveDealForReviewAndThankyou(thankYouResponse.getHotelDetails());
        }
        thankYouResponse.setExpVariantKeys(persistanceMultiRoomResponseEntity.getPersistedData().getExpVariantKeys());
        if(persistanceMultiRoomResponseEntity.getPersistedData().getLuckyUserContext() != null){
            thankYouResponse.setLuckyUserContext(LuckyUserContext.LUCKY.equals(persistanceMultiRoomResponseEntity.getPersistedData().getLuckyUserContext()) ? PRIVILEGED_USER : LuckyUserContext.LUCKY_UNLUCKY.equals(persistanceMultiRoomResponseEntity.getPersistedData().getLuckyUserContext()) ? CURSED_USER : UNFORTUNATE_USER);
        }
        CommonModifierResponse commonModifierResponse = buildCommonModifierFromTxnData(persistanceMultiRoomResponseEntity.getPersistedData());
        String luckyUserContext = utility.logLuckyUserData(commonModifierResponse, thankYouResponse.getLuckyUserContext(), "thank-you");
        thankYouResponse.setLuckyUserContext(luckyUserContext);
        int ancillaryVariant = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey()) ? Integer.parseInt(expDataMap.get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
        boolean isCashBackCard = ancillaryVariant != 0 && persistanceMultiRoomResponseEntity != null && persistanceMultiRoomResponseEntity.getPersistedData() != null &&
                (persistanceMultiRoomResponseEntity.getPersistedData().getMmtIhCabCashback() > 0.0 || persistanceMultiRoomResponseEntity.getPersistedData().getMmtIhForexCashback() > 0.0)? true : false;
        String cabsDeepLinkUrl = null;
        if(persistanceMultiRoomResponseEntity != null && persistanceMultiRoomResponseEntity.getPersistedData() != null
        && persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare() != null && persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().getDisplayPriceBreakDown() != null
        && persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null) {
            cabsDeepLinkUrl = buildCabsDeepLinkUrl(persistanceMultiRoomResponseEntity.getPersistedData());
            CouponInfo appliedCouponInfo = commonHelper.getAppliedCoupon(persistanceMultiRoomResponseEntity.getPersistedData());
            thankYouResponse.setBenefitDeals(buildBenefitDeals(persistanceMultiRoomResponseEntity.getPersistedData().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo(), appliedCouponInfo, Arrays.asList(persistanceMultiRoomResponseEntity.getPersistedData().getHydraSegments()),cabsDeepLinkUrl, isCashBackCard));
        }
        if (isThankyouV2 || isGlobalEntity(persistanceMultiRoomResponseEntity.getPersistedData())) {
            thankYouResponse.setBlackInfo(null);
        } else {
            thankYouResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(persistanceMultiRoomResponseEntity.getPersistedData().getBlackInfo()));
        }
        //setting Benfit INFO in the thank you page response.
        if (!isThankyouV2 && persistanceMultiRoomResponseEntity != null && persistanceMultiRoomResponseEntity.getPersistedData() != null &&
                persistanceMultiRoomResponseEntity.getPersistedData().getHotelBenefitInfo() != null) {
            thankYouResponse.setHotelBenefits(commonResponseTransformer.buildBenefitInfo(persistanceMultiRoomResponseEntity.getPersistedData().getHotelBenefitInfo()));
        }

        if (persistanceMultiRoomResponseEntity != null && persistanceMultiRoomResponseEntity.getPersistedData() != null &&
                persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo() != null) {
            thankYouResponse.setTrackingText(persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getTrackingText());
            if (persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails() != null) {
                if (persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().isFlexiCancelApplied() &&
                        StringUtils.equalsIgnoreCase(persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getCancellationType(), FREE_CANCELLATION)) {
                    thankYouResponse.setTrackingText(concatText(thankYouResponse.getTrackingText(), "FLX_Opted_FC"));
                } else if (persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().isFlexiCancelApplied() &&
                        StringUtils.equalsIgnoreCase(persistanceMultiRoomResponseEntity.getPersistedData().getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getCancellationType(), NON_REFUNDABLE)) {
                    thankYouResponse.setTrackingText(concatText(thankYouResponse.getTrackingText(), "FLX_Opted_NR"));
                }
            }
        }
        String PayModeAndMealPlanInTrackingText = appendPayModeAndMealPlanInTrackingText(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        buildTrackingMap(thankYouResponse, "m_c71", thankYouResponse.getResponseTrackingKey());
        buildTrackingMap(thankYouResponse, "m_c8", thankYouResponse.getTrackingText());
        buildTrackingMap(thankYouResponse, "m_c11", PayModeAndMealPlanInTrackingText);
        buildTrackingMap(thankYouResponse, "m_c1", buildSpecialRatePlanTrackingText(persistanceMultiRoomResponseEntity.getPersistedData()));
        Map<String, String> trackingMap = persistanceMultiRoomResponseEntity.getPersistedData().getTrackingMap();
        if (trackingMap != null) {
            for (Map.Entry<String, String> entry : trackingMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                buildTrackingMap(thankYouResponse, key, value);
            }
        }
        if(!isThankyouV2){
            thankYouResponse.setLongStayBenefits(commonResponseTransformer.buildLongStayBenefits(persistanceMultiRoomResponseEntity.getPersistedData().getLongStayBenefits()));
        }
        updateCurrencySymbol(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        updateInsuranceInfo(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        thankYouResponse.setCardsMap(buildCardsMap(persistanceMultiRoomResponseEntity.getPersistedData(),expDataMap,cabsDeepLinkUrl, isThankyouV2));
        thankYouResponse.setCurrency(persistanceMultiRoomResponseEntity.getPersistedData().getCurrency());
        if (isHotelCloudRateSegment(persistanceMultiRoomResponseEntity)) {
            thankYouResponse.setHotelCloudCallOutData(buildHotelCloudCallOutData());
        }

        // thank you page revamp
        if(isThankyouV2){
            thankYouResponse.setBookingUIData(buildThankYouUIData(thankYouResponse, persistanceMultiRoomResponseEntity));
        }

        PixelUrlConfig pixelUrlData = createPixelUrlData(persistanceMultiRoomResponseEntity.getPersistedData(), parameterMap, thankYouResponse);
        if(pixelUrlData != null) {
            thankYouResponse.setPixelUrl(commonResponseTransformer.buildPixelUrl(pixelUrlData));
        }
        // Inside convertThankYouResponse method, after building other response objects
        if (persistanceMultiRoomResponseEntity.getPersistedData() != null &&
                CollectionUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getHotelList()) &&
                persistanceMultiRoomResponseEntity.getPersistedData().getHotelList().get(0) != null) {
            MultiRoomStayDetails multiRoomStayDetails = new MultiRoomStayDetails();
            MultiRoomStayDetails.PopupData popupData = commonResponseTransformer.buildMultiRoomStayDetailsPopupData(persistanceMultiRoomResponseEntity.getPersistedData().getHotelList().get(0).getTariffInfoList());
            if(popupData != null){
                multiRoomStayDetails.setPopupData(popupData);
                thankYouResponse.setMultiRoomStayDetails(multiRoomStayDetails);
            }
        }
        return thankYouResponse;
    }

    private PixelUrlConfig createPixelUrlData(PersistedMultiRoomData thankYouResponse, Map<String, String> parameterMap, ThankYouResponse response) {
        if(utility.isGccOrKsa() || utility.isMyBizRequest() || !utility.isB2CFunnel() || thankYouResponse == null || thankYouResponse.getAvailReqBody() == null || !utility.isB2CFunnel(thankYouResponse.getAvailReqBody().getIdContext()) || thankYouResponse.getAvailReqBody().getLocationId() == null || !pixelTrackingLocations.contains(thankYouResponse.getAvailReqBody().getLocationId())){
            return null;
        }
        HotelInfo hotelInfo = null;
        if(thankYouResponse != null && thankYouResponse.getHotelList() != null && !thankYouResponse.getHotelList().isEmpty() && thankYouResponse.getHotelList().get(0).getHotelInfo() != null){
            hotelInfo = thankYouResponse.getHotelList().get(0).getHotelInfo();
        }
        return new PixelUrlConfig(thankYouResponse.getAvailReqBody().getCurrency(), parameterMap.get(LANGUAGE), hotelInfo.getCityName(), response != null && response.getTotalAmount() != null ? String.valueOf(response.getTotalAmount().getAmount()) : null, hotelInfo.getName(), thankYouResponse.getAvailReqBody().getCheckin(), thankYouResponse.getAvailReqBody().getCheckout(), thankYouResponse.getAvailReqBody().getRoomStayCandidates() != null && !thankYouResponse.getAvailReqBody().getRoomStayCandidates().isEmpty() ? getGuestCountForPixelUrl(thankYouResponse.getAvailReqBody().getRoomStayCandidates()) : null, parameterMap != null && parameterMap.containsKey(USER_COUNTRY) ? parameterMap.get(USER_COUNTRY) : null, thankYouResponse.getAvailReqBody().getDeviceId(), thankYouResponse.getAvailReqBody().getRequestIdentifier() != null ? thankYouResponse.getAvailReqBody().getRequestIdentifier().getJourneyId() : null);
    }

    private int getGuestCountForPixelUrl(List<RoomStayCandidate> roomStayCandidates) {
        if(roomStayCandidates != null && !roomStayCandidates.isEmpty()){
            int adultCount = 0;
            int childcount = 0;
            for(RoomStayCandidate roomStayCandidate: roomStayCandidates) {
                if(roomStayCandidate.getGuestCounts() != null && !roomStayCandidate.getGuestCounts().isEmpty()){
                    adultCount += Integer.parseInt(roomStayCandidate.getGuestCounts().get(0).getCount());
                    if(roomStayCandidate.getGuestCounts().get(0).getAges() != null && !roomStayCandidate.getGuestCounts().get(0).getAges().isEmpty()){
                        childcount += roomStayCandidate.getGuestCounts().get(0).getAges().size();
                    }
                }
            }
            return adultCount + childcount;
        }
        return 0;
    }

    private boolean isThankyouV2(Map<String, String> expDataMap) {
        return MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(CLIENT_EXP_Thankyou_PAGE_REDESIGN) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(CLIENT_EXP_Thankyou_PAGE_REDESIGN));
    }

    private boolean isGlobalEntity(PersistedMultiRoomData persistedData) {
       return persistedData.getUserGlobalInfo() != null
                && StringUtils.isNotEmpty(persistedData.getUserGlobalInfo().getEntityName())
                && "GLOBAL".equalsIgnoreCase(persistedData.getUserGlobalInfo().getEntityName());
    }

    private ThankYouUIData buildThankYouUIData(ThankYouResponse thankYouResponse, PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {

        ThankYouUIData uiData = new ThankYouUIData();

        uiData.setStatusInfo(buildBookingStatusInfo(thankYouResponse.getBookingDetails(), isZeroBNPLOrPAH(thankYouResponse.getPaidAmount())));
        if(thankYouResponse.getBookingDetails() != null && thankYouResponse.getBookingDetails().getStatus() != BookingStatus.FAILED){

            uiData.setPaymentInfo(buildPaymentInfo(persistanceMultiRoomResponseEntity));
            uiData.setBookingInfo(buildInfoItems(thankYouResponse, persistanceMultiRoomResponseEntity));
            uiData.setInclusions(buildInclusionsV2(thankYouResponse,persistanceMultiRoomResponseEntity));
            uiData.setCharityInfo(buildCharityData(persistanceMultiRoomResponseEntity.getPersistedData()));

            if(BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())){
                uiData.setPrimaryCTA(new Cta());
                uiData.getPrimaryCTA().setTitle(polyglotService.getTranslatedData(THANK_YOU_PRIMARY_CTA_TITLE));
                uiData.getPrimaryCTA().setDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistanceMultiRoomResponseEntity.getPersistedData().getUserGlobalInfo(), persistanceMultiRoomResponseEntity.getPersistedData().getExpData()), persistanceMultiRoomResponseEntity.getPersistedData().getBookingMetaInfo()));
            }
        }

        if(uiData.getStatusInfo() != null || uiData.getPaymentInfo() != null || uiData.getInclusions() != null || uiData.getBookingInfo() != null || uiData.getCharityInfo() != null ){
            return uiData;
        }
        return null;
    }

    private CharityData buildCharityData(PersistedMultiRoomData persistedData) {
        if(persistedData.getAddOnInfo() != null && CollectionUtils.isNotEmpty(persistedData.getAddOnInfo().getAddOnNode())){
            Optional<AddOnNode> charityAddOn = persistedData.getAddOnInfo().getAddOnNode().stream().filter(addOn -> Objects.equals(addOn.getId(), "CHARITY_ID_V2")).findFirst();
            if(charityAddOn.isPresent() && charityAddOn.get().getPrice() != null){
                String currency = getCurrencySymbol(persistedData.getCurrency());
                String amount = charityAddOn.get().getPrice().toString();
                return new CharityData(
                        polyglotService.getTranslatedData(THANK_YOU_CHARITY_TITLE).replace("<amount>",  currency + amount),
                        polyglotService.getTranslatedData(THANK_YOU_CHARITY_SUB_TITLE),
                        charityAddOn.get().getBgColor(),
                        charityAddOn.get().getImageUrl()
                );
            }
        }
        return null;
    }

    private List<ThankYouInclusion> buildInclusionsV2(ThankYouResponse thankYouResponse, PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {

        List<ThankYouInclusion> items = new ArrayList<>();

        BookedCancellationPolicy commonCancellationPolicy = getCommonCancellationPolicy(thankYouResponse);
        if(commonCancellationPolicy != null && StringUtils.isNotEmpty(commonCancellationPolicy.getText())){
            String title = commonCancellationPolicy.getText();
            if(StringUtils.isNotEmpty(commonCancellationPolicy.getSubText()))
                title += NEXT_LINE + commonCancellationPolicy.getSubText();
            items.add(new ThankYouInclusion(title, commonCancellationPolicy.getIconUrl(), ThankYouViewType.COLLAPSE));
        }

        List<InsuranceInfo> insuranceInfoList = buildInsuranceInfo(persistanceMultiRoomResponseEntity.getPersistedData());
        if(CollectionUtils.isNotEmpty(insuranceInfoList)){
            InsuranceInfo insuranceInfo = insuranceInfoList.get(0);
            items.add(new ThankYouInclusion(insuranceInfo.getSubText(),insuranceInfo.getIconUrl(), ThankYouViewType.ALWAYS));
        }

        String checkInPolicy = getCheckInPolicyDescription(persistanceMultiRoomResponseEntity.getPersistedData());
        if(StringUtils.isNotEmpty(checkInPolicy)){
            items.add(new ThankYouInclusion(checkInPolicy, inclusionIconRedInfo, ThankYouViewType.ALWAYS));
        }

        if (!isGlobalEntity(persistanceMultiRoomResponseEntity.getPersistedData())) {
            BlackInfo blackInfo = commonResponseTransformer.buildBlackInfo(persistanceMultiRoomResponseEntity.getPersistedData().getBlackInfo());
            if(blackInfo != null && CollectionUtils.isNotEmpty(blackInfo.getInclusionsList())){
                Inclusion inclusion = blackInfo.getInclusionsList().get(0);
                items.add(new ThankYouInclusion(inclusion.getValue(), inclusion.getIconUrl(), ThankYouViewType.ALWAYS));
            }
        }

        LongStayBenefits longStayBenefits = commonResponseTransformer.buildLongStayBenefits(persistanceMultiRoomResponseEntity.getPersistedData().getLongStayBenefits());
        if(longStayBenefits != null && CollectionUtils.isNotEmpty(longStayBenefits.getInclusionsList())){
            String longStayBenefitText = "";
            if(StringUtils.isNotEmpty(longStayBenefits.getTitle())){
                String titleColor = StringUtils.isNotEmpty(longStayBenefits.getTitleColor()) ? longStayBenefits.getTitleColor() : "#000000";
                longStayBenefitText += String.format(PERSUASION_TEXT_TEMPLATE, titleColor, OPEN_BOLD_TAG + longStayBenefits.getTitle() + CLOSE_BOLD_TAG);
                longStayBenefitText += SPACE;
            }
            longStayBenefitText += longStayBenefits.getInclusionsList().stream()
                    .map(Inclusion::getValue)
                    .collect(Collectors.joining(COMMA));
            String iconUrl = longStayBenefits.getInclusionsList().get(0).getIconUrl();
            items.add(new ThankYouInclusion(longStayBenefitText, iconUrl, ThankYouViewType.ALWAYS));
        }

        HotelBenefits hotelBenefits = commonResponseTransformer.buildBenefitInfo(persistanceMultiRoomResponseEntity.getPersistedData().getHotelBenefitInfo());
        if(hotelBenefits != null){
            String benefitText = Optional.ofNullable(hotelBenefits.getDescription()).orElse("");
            if(!benefitText.isEmpty()){
                items.add(new ThankYouInclusion(benefitText, hotelBenefits.getIconUrl(), ThankYouViewType.ALWAYS));
            }
        }

        OnlyForTodayPersuasionDetails onlyTodayDeal = persistanceMultiRoomResponseEntity.getPersistedData().getOnlyForTodayPersuasionDetails();
        if(onlyTodayDeal != null && onlyTodayDeal.isEnabled()){
            items.add(new ThankYouInclusion(onlyTodayDeal.getTitle(), onlyTodayDealiconUrl, ThankYouViewType.COLLAPSE));
        }


        items.forEach( item -> {
            if(StringUtils.isEmpty(item.getIconUrl())){
                item.setIconUrl(inclusionIconSingleTickGrey);
            }
        });

        if(!items.isEmpty())
            return items;

        return null;
    }

    private BookedCancellationPolicy getCommonCancellationPolicy(ThankYouResponse thankYouResponse) {
        BookedRooms roomInfo = thankYouResponse.getRooms();

        AtomicBoolean isCommonCancellationPolicy = new AtomicBoolean(true);
        if(roomInfo != null && CollectionUtils.isNotEmpty(roomInfo.getRatePlanList())){

            BookedCancellationPolicyType firstRPCancellationPolicyType = getCancellationPolicyType(roomInfo.getRatePlanList().get(0).getCancellationPolicy());
            roomInfo.getRatePlanList().forEach( ratePlan -> {
                BookedCancellationPolicyType cancellationPolicyType = getCancellationPolicyType(ratePlan.getCancellationPolicy());
                if(firstRPCancellationPolicyType != cancellationPolicyType){
                    isCommonCancellationPolicy.set(false);
                }
            });

            if(isCommonCancellationPolicy.get()){
                return roomInfo.getRatePlanList().get(0).getCancellationPolicy();
            }

        }
        return null;
    }

    private BookedCancellationPolicyType getCancellationPolicyType(BookedCancellationPolicy cancellationPolicy) {
        if(cancellationPolicy != null)
            return cancellationPolicy.getType();
        return null;
    }

    private List<ThankYouInfoItem> buildInfoItems(ThankYouResponse thankYouResponse, PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {

        List<ThankYouInfoItem> items = new ArrayList<>();

        // Entire Apartment
        if(thankYouResponse.getHotelDetails() != null && BooleanUtils.isTrue(thankYouResponse.getHotelDetails().isEntireProperty())){
            items.add(new ThankYouInfoItem(
                    thankYouResponse.getHotelDetails().getGuestRoomValue(),
                    null,
                    Optional.ofNullable(thankYouResponse.getHotelDetails().getBedInfoText()).orElse(""),
                    ThankYouInfoItemType.VERTICAL,
                    ThankYouViewType.ALWAYS)
            );
        }

        // Primary Guest

        if(CollectionUtils.isNotEmpty(thankYouResponse.getTravellers())){
            String travellerName = getTravellerName(thankYouResponse.getTravellers().get(0));
            if(!StringUtils.isEmpty(travellerName)){
                items.add(new ThankYouInfoItem(
                        polyglotService.getTranslatedData(LABEL_PRIMARY_GUEST),
                        null,
                        travellerName,
                        ThankYouInfoItemType.HORIZONTAL,
                        ThankYouViewType.EXPAND)
                );
            }
        }


        // Guest & Rooms
        String occupancyText = buildOccupancyText(thankYouResponse);
        String occupancyLabel = polyglotService.getTranslatedData(LABEL_GUEST_AND_ROOMS);
        if(thankYouResponse.getHotelDetails() != null && thankYouResponse.getHotelDetails().getGuestRoomKey() != null){
            occupancyLabel = thankYouResponse.getHotelDetails().getGuestRoomKey();
        }
        if(!StringUtils.isEmpty(occupancyText)) {
            items.add(new ThankYouInfoItem(
                    occupancyLabel,
                    null,
                    occupancyText,
                    ThankYouInfoItemType.HORIZONTAL,
                    ThankYouViewType.EXPAND)
            );
        }

        // Amount Paid
        if(thankYouResponse.getPaidAmount() != null && thankYouResponse.getPaidAmount().getAmount() != null){
            String currencySymbol = getCurrencySymbol(thankYouResponse.getPaidAmount().getCurrency());
            String amount = formatDouble(thankYouResponse.getPaidAmount().getAmount());
            if(!amount.isEmpty()){
                items.add(new ThankYouInfoItem(
                        polyglotService.getTranslatedData(LABEL_AMOUNT_PAID),
                        thankYouResponse.getPaidAmount().getSubtitle(),
                        currencySymbol + amount,
                        ThankYouInfoItemType.HORIZONTAL,
                        ThankYouViewType.ALWAYS)
                );
            }
        }

        // Total Price
        if(thankYouResponse.getTotalAmount() != null && thankYouResponse.getTotalAmount().getAmount() != null){
            String currencySymbol = getCurrencySymbol(thankYouResponse.getTotalAmount().getCurrency());
            String amount = formatDouble(thankYouResponse.getTotalAmount().getAmount());
            if(!amount.isEmpty()){
                items.add(new ThankYouInfoItem(
                        polyglotService.getTranslatedData(LABEL_TOTAL_PRICE),
                        thankYouResponse.getTotalAmount().getSubtitle(),
                        currencySymbol + amount,
                        ThankYouInfoItemType.HORIZONTAL,
                        ThankYouViewType.EXPAND)
                );
            }
        }

        // Booking Id
        String bookingId = thankYouResponse.getBookingDetails().getBookingId();
        if(!StringUtils.isEmpty(bookingId)){
            items.add(new ThankYouInfoItem(
                    polyglotService.getTranslatedData(LABEL_Booking_ID),
                    null,
                    bookingId,
                    ThankYouInfoItemType.HORIZONTAL,
                    ThankYouViewType.EXPAND)
            );
        }

        // PNR
        String pnr = thankYouResponse.getBookingDetails().getPnr();
        if(!StringUtils.isEmpty(pnr)){
            items.add(new ThankYouInfoItem(
                    polyglotService.getTranslatedData(LABEL_PNR),
                    null,
                    pnr,
                    ThankYouInfoItemType.HORIZONTAL,
                    ThankYouViewType.EXPAND)
            );
        }

        if(!items.isEmpty())
            return items;

        return null;
    }

    private String buildOccupancyText(ThankYouResponse thankYouResponse) {
        BookedRooms roomInfo = thankYouResponse.getRooms();
        if(roomInfo != null){
            AtomicInteger totalAdultCount = new AtomicInteger(0);
            AtomicInteger totalChildCount = new AtomicInteger(0);
            AtomicReference<String> combinedChildAgeString = new AtomicReference<>("");

            boolean isGroupBooking = thankYouResponse.getHotelDetails() != null && BooleanUtils.isTrue(thankYouResponse.getHotelDetails().getGroupBookingPrice());
            boolean isSingleRatePlan = roomInfo.getRatePlanList() != null && roomInfo.getRatePlanList().size() == 1 ;
            boolean shouldIncludeChildAge = isGroupBooking || isSingleRatePlan ;

            if(CollectionUtils.isNotEmpty(roomInfo.getRatePlanList())){
                roomInfo.getRatePlanList().forEach( ratePlan -> {
                    BookedOccupancy bookedOccupancy = ratePlan.getOccupancy();
                    if(bookedOccupancy != null) {

                        AtomicReference<String> childAges = new AtomicReference<>("");
                        if(bookedOccupancy.getAdult() != null){
                            totalAdultCount.addAndGet(bookedOccupancy.getAdult());
                        }
                        if(bookedOccupancy.getChild() != null){
                            if(bookedOccupancy.getChild().getCount() != null)
                                totalChildCount.addAndGet(bookedOccupancy.getChild().getCount());
                            if(bookedOccupancy.getChild().getAges() != null)
                                childAges.set(String.join(COMMA_SPACE, bookedOccupancy.getChild().getAges()));
                        }

                        if(shouldIncludeChildAge){

                            if(!combinedChildAgeString.get().isEmpty() && !childAges.get().isEmpty()){
                                combinedChildAgeString.updateAndGet(str -> str + COMMA);
                            }
                            combinedChildAgeString.updateAndGet(str -> str + childAges.get());
                        }
                    }
                });
            }

            String adultCountText = null;
            if(totalAdultCount.get() == 1)
                adultCountText = MessageFormat.format(polyglotService.getTranslatedData(LABEL_ADULT_COUNT),totalAdultCount.get());
            else  if(totalAdultCount.get() > 1)
                adultCountText = MessageFormat.format(polyglotService.getTranslatedData(LABEL_ADULT_COUNT_PLURAL),totalAdultCount.get());

            String childCountText = null;
            if(totalChildCount.get() == 1)
                childCountText = MessageFormat.format(polyglotService.getTranslatedData(LABEL_CHILD_COUNT),totalChildCount.get());
            else if(totalChildCount.get() > 1)
                childCountText = MessageFormat.format(polyglotService.getTranslatedData(LABEL_CHILD_COUNT_PLURAL),totalChildCount.get());

            StringBuilder occupancyText = new StringBuilder();

            if (StringUtils.isNotEmpty(adultCountText)) {
                occupancyText.append(adultCountText);
            }

            if (StringUtils.isNotEmpty(childCountText)) {
                if(StringUtils.isNotEmpty(occupancyText)){
                    occupancyText.append(COMMA).append(SPACE);
                }
                occupancyText.append(childCountText);

                if (!StringUtils.isEmpty(combinedChildAgeString.get())) {
                    occupancyText.append(OPEN_PARENTHESIS)
                            .append(combinedChildAgeString.get())
                            .append(CLOSE_PARENTHESIS);
                }
            }

            if(thankYouResponse.getHotelDetails() != null && thankYouResponse.getHotelDetails().getGuestRoomValue() != null && BooleanUtils.isNotTrue(thankYouResponse.getHotelDetails().isEntireProperty())){
                if(StringUtils.isNotEmpty(occupancyText)){
                    occupancyText.append(COMMA + SPACE);
                }
                occupancyText.append(thankYouResponse.getHotelDetails().getGuestRoomValue());
            }

            return occupancyText.toString();
        }
        return null;
    }

    private String getTravellerName(Traveller traveller) {
        if(traveller != null){
            String fullName = "";
            if(!StringUtils.isEmpty(traveller.getFirstName())){
                fullName += traveller.getFirstName();
            }
            if(!StringUtils.isEmpty(traveller.getLastName())){
                fullName += SPACE;
                fullName += traveller.getLastName();
            }
            return fullName.trim();
        }
        return null;
    }

    private PaymentInfo buildPaymentInfo(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {
        List<AmountDetail> amountBreakup = buildAmountBreakupV2(persistanceMultiRoomResponseEntity.getPersistedData());
        if(amountBreakup != null){
            AmountDetail pendingAmount = amountBreakup.stream().filter(amountDetail -> amountDetail.getKey().equals("PENDING_AMOUNT")).findFirst().orElse(null);
            AmountDetail paidAmount = amountBreakup.stream().filter(amountDetail -> amountDetail.getKey().equals("PAID_AMOUNT")).findFirst().orElse(null);

            String paidAmountText = buildPaidAmountText(paidAmount);
            String pendingAmountText = buildPendingAmountText(pendingAmount);

            if(paidAmountText != null && pendingAmountText != null){
                PaymentInfo paymentInfo = new PaymentInfo();
                paymentInfo.setTitle(paidAmountText + " : " + pendingAmountText);
                paymentInfo.setBgColor("#E6FFF9");
                return paymentInfo;
            }
        }

        return null;
    }

    private String buildPendingAmountText(AmountDetail pendingAmount) {
        if(pendingAmount == null) return null;
        String currencySymbol = getCurrencySymbol(pendingAmount.getCurrency());
        String altCurrencySymbol = null;
        if(pendingAmount.getAlternateCurrency() != null){
            altCurrencySymbol = getCurrencySymbol(pendingAmount.getAlternateCurrency());
        }
        if(pendingAmount.getSubtitle() != null && !pendingAmount.getSubtitle().isEmpty() && pendingAmount.getAmount() != null){
            if(altCurrencySymbol != null && pendingAmount.getAlternateAmount() != null){
                String pendingAmountText = formatDouble(pendingAmount.getAlternateAmount());
                return pendingAmount.getSubtitle()
                        .replace("<amount>", OPEN_FONT_TAG_00000 + OPEN_BOLD_TAG + altCurrencySymbol + pendingAmountText + CLOSE_BOLD_TAG + CLOSE_FONT_TAG + SPACE + OPEN_PARENTHESIS + currencySymbol + pendingAmount.getAmount().toString() + CLOSE_PARENTHESIS);
            }else{
                String pendingAmountText = formatDouble(pendingAmount.getAmount());
                return pendingAmount.getSubtitle()
                        .replace("<amount>", OPEN_FONT_TAG_00000 + OPEN_BOLD_TAG + currencySymbol + pendingAmountText +  CLOSE_BOLD_TAG + CLOSE_FONT_TAG);
            }


        }
        return null;
    }



    private String getCurrencySymbol(String currency) {
        return  Currency.getCurrencyEnum(currency).getCurrencySymbol();
    }

    private String buildPaidAmountText(AmountDetail paidAmount) {
        if(paidAmount == null) return null;
        String currencySymbol = getCurrencySymbol(paidAmount.getCurrency());
        if(paidAmount.getAmount() != null){
            String paidAmountStr = formatDouble(paidAmount.getAmount());
            String translatedText = polyglotService.getTranslatedData(BNPL_PAID_AMOUNT_BREAKUP_TITLE).replace("<amount>", currencySymbol + paidAmountStr);
            return (OPEN_FONT_TAG_007E7D + OPEN_BOLD_TAG + translatedText + CLOSE_BOLD_TAG + CLOSE_FONT_TAG);
        }
        return null;
    }

    public String formatDouble(Double num) {
        if (num == null) {
            return ""; // Handle null case safely
        }
        if (num == num.longValue()) { // Check if the value is a whole number
            return String.valueOf(num.longValue());
        } else {
            return String.valueOf(num);
        }
    }

    private BookingStatusInfo buildBookingStatusInfo(BookingDetails bookingDetails, boolean isZeroBNPLOrPAH) {
        if(bookingDetails == null || bookingDetails.getStatus() == null) return null;
        BookingStatusInfo info  = new BookingStatusInfo();
        switch (bookingDetails.getStatus()){
            case SUCCESS:{
                info.setTitle(OPEN_FONT_TAG_007E7D + OPEN_BOLD_TAG + polyglotService.getTranslatedData(HEADER_TITLE_SUCCESS) + CLOSE_BOLD_TAG + CLOSE_FONT_TAG);
                info.setSubtitle(getSuccessHeaderSubtitle(bookingDetails));
                info.setIconUrl(headerIconSuccess);
                return info;
            }
            case FAILED:{
                info.setTitle(OPEN_FONT_TAG_EC2127 + OPEN_BOLD_TAG + polyglotService.getTranslatedData(HEADER_TITLE_FAILED) + CLOSE_BOLD_TAG + CLOSE_FONT_TAG);
                if(isZeroBNPLOrPAH){
                    info.setSubtitle(polyglotService.getTranslatedData(HEADER_SUBTITLE_FAILED_BNPL));
                }else{
                    info.setSubtitle(polyglotService.getTranslatedData(HEADER_SUBTITLE_FAILED_PAID));
                }
                info.setIconUrl(headerIconFailed);
                return info;
            }
            case PENDING:{
                String fontOpenStr = OPEN_FONT_TAG_CF8100 + OPEN_BOLD_TAG ;
                String fontCloseStr = CLOSE_BOLD_TAG + CLOSE_FONT_TAG ;
                if(isRTB(bookingDetails)){
                    if(isZeroBNPLOrPAH){
                        info.setTitle(fontOpenStr + polyglotService.getTranslatedData(HEADER_TITLE_PENDING_BNPL_RTB) + fontCloseStr);
                    }else{
                        info.setTitle(fontOpenStr+ polyglotService.getTranslatedData(HEADER_TITLE_PENDING_PAID_RTB) + fontCloseStr);
                    }
                    info.setSubtitle(getPendingHeaderSubtitleRTB(bookingDetails));
                }else{
                    if(isZeroBNPLOrPAH){
                        info.setTitle(fontOpenStr + polyglotService.getTranslatedData(HEADER_TITLE_PENDING_BNPL) + fontCloseStr);
                    }else{
                        info.setTitle(fontOpenStr + polyglotService.getTranslatedData(HEADER_TITLE_PENDING_PAID) + fontCloseStr);
                    }
                    info.setSubtitle(getPendingHeaderSubtitle(bookingDetails));
                }
                info.setIconUrl(headerIconPending);
                return info;
            }
            default:{
                return null;
            }
        }
    }

    private boolean isRTB(BookingDetails bookingDetails){
        Boolean rtbPreApproved = bookingDetails.getRtbPreApproved();
        Boolean isRTB = bookingDetails.getRequestToBook();
        return isRTB == true && rtbPreApproved != true;
    }

    private boolean isZeroBNPLOrPAH(AmountDetail paidAmount){
        return paidAmount == null || (paidAmount.getAmount() != null && paidAmount.getAmount() == 0.0);
    }

    private String getSuccessHeaderSubtitle(BookingDetails bookingDetails) {
        String email = getBookerEmail(bookingDetails);
        String mobileNum = getBookerMobile(bookingDetails);
        if(!email.isEmpty() && !mobileNum.isEmpty()){
            return polyglotService.getTranslatedData(HEADER_SUBTITLE_SUCCESS).replace("{email}",email).replace("{number}",mobileNum);
        }
        return null;
    }

    private String getPendingHeaderSubtitleRTB(BookingDetails bookingDetails) {
        String pendingTime = getPendingTime(bookingDetails);
        if(!pendingTime.isEmpty()){
            return polyglotService.getTranslatedData(HEADER_SUBTITLE_PENDING_RTB).replace("{pendingTime}",pendingTime);
        }
        return null;
    }

    private String getPendingHeaderSubtitle(BookingDetails bookingDetails) {
        String email = getBookerEmail(bookingDetails);
        String mobileNum = getBookerMobile(bookingDetails);
        String pendingTime = getPendingTime(bookingDetails);
        if(!email.isEmpty() && !mobileNum.isEmpty()){
            return polyglotService.getTranslatedData(HEADER_SUBTITLE_PENDING).replace("{email}",email).replace("{number}",mobileNum).replace("{pendingTime}",pendingTime);
        }
        return null;
    }

    private String getBookerEmail(BookingDetails bookingDetails) {
        if(bookingDetails != null && bookingDetails.getBookerInfo() != null && bookingDetails.getBookerInfo().getEmailId() != null)
            return bookingDetails.getBookerInfo().getEmailId();

        return "";
    }

    private String getPendingTime(BookingDetails bookingDetails) {
        if(bookingDetails != null && bookingDetails.getPendingBookingCardInfo() != null && bookingDetails.getPendingBookingCardInfo().getPendingTime() !=null)
            return bookingDetails.getPendingBookingCardInfo().getPendingTime();

        return "";
    }

    private String getBookerMobile(BookingDetails bookingDetails) {
        if(bookingDetails != null && bookingDetails.getBookerInfo() != null && bookingDetails.getBookerInfo().getMobileNum() != null)
            return bookingDetails.getBookerInfo().getMobileNum();

        return "";
    }

    private boolean isHotelCloudRateSegment(PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {
        if (persistanceMultiRoomResponseEntity != null
                && persistanceMultiRoomResponseEntity.getPersistedData() != null
                && CollectionUtils.isNotEmpty(persistanceMultiRoomResponseEntity.getPersistedData().getHotelList())) {
            List<PersistedTariffInfo> tariffInfoList = persistanceMultiRoomResponseEntity.getPersistedData().getHotelList().get(0).getTariffInfoList();
            if (CollectionUtils.isNotEmpty(tariffInfoList)) {
                for (PersistedTariffInfo tariffInfo : tariffInfoList) {
                    if (!HOTEL_CLOUD_RATE_SEGMENT.equals(tariffInfo.getSegmentId())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public OnlyTodayDealInfo setOnlyTodayDealInfo(OnlyForTodayPersuasionDetails onlyForTodayPersuasionDetails) {
        OnlyTodayDealInfo onlyTodayDealInfo = new OnlyTodayDealInfo();
        if(onlyForTodayPersuasionDetails.getTitle() != null) {
            onlyTodayDealInfo.setTitle(onlyForTodayPersuasionDetails.getTitle());
            onlyTodayDealInfo.setIconUrl(onlyTodayDealiconUrl);
            onlyTodayDealInfo.setTitleColor("#4A4A4A");
            onlyTodayDealInfo.setBackgroundColor("#E6FFF9");
        }
        return onlyTodayDealInfo;
    }

    private String buildSpecialRatePlanTrackingText(PersistedMultiRoomData persistedData) {
        StringBuilder trackingText = new StringBuilder();
        String specialRatePlan = persistedData.getAvailReqBody().getRatePlanType();
        if(StringUtils.isNotEmpty(specialRatePlan) && LINKED_RATE_PLAN_TYPE.equalsIgnoreCase(specialRatePlan)){
            trackingText.append("linked_rate_booked");
        }
        return trackingText.toString();
    }

    private Map<String, CardInfo> buildCardsMap(PersistedMultiRoomData persistedData, Map<String, String> expDataMap, String cabsDeepLinkUrl, boolean isThankyouV2) {
        Map<String, CardInfo> cardsMap = new HashMap<>();
        try {
            if(null == persistedData.getAvailReqBody()){
                return cardsMap;
            }
            String countryCode = persistedData.getAvailReqBody().getCountryCode();
            String siteDomain = persistedData.getAvailReqBody().getSiteDomain();
            String cityCode = persistedData.getAvailReqBody().getCityCode();

            //building Lowest Price Guarantee card
            Map<String, CardInfo> lpgCardsMap = buildLpgCardMap(persistedData);
            if (MapUtils.isNotEmpty(lpgCardsMap)) {
                cardsMap.putAll(lpgCardsMap);
            }
            boolean bookingDeviceDesktop = DEVICE_OS_DESKTOP.equalsIgnoreCase(persistedData.getAvailReqBody().getBookingDevice());
            //building forex and cab card
            if (utility.isB2CFunnel(persistedData.getAvailReqBody().getIdContext()) && utility.isIHFunnel(countryCode, siteDomain)
                    && (utility.isExperimentOn(expDataMap, ExperimentKeys.forexCard.getKey()) || utility.isExperimentOn(expDataMap, ExperimentKeys.forexCard.getKey()))) {
                Map<String, CardInfo> forexAndCabCard = buildForexAndCabCard(expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop, isThankyouV2);
                if (MapUtils.isNotEmpty(forexAndCabCard)) {
                    cardsMap.putAll(forexAndCabCard);
                }
            }

            if (persistedData.isDigiLockerEnabled()) {
                String bookingId = persistedData.getBookingMetaInfo() != null ? persistedData.getBookingMetaInfo().getBookingId() : null;
                if (StringUtils.isNotEmpty(bookingId)) {
                    CardInfo digiLockerCard = buildDigiLockerCard(persistedData, bookingId);
                    if (digiLockerCard != null) {
                        cardsMap.put(DIGILOCKER_CARD_ID, digiLockerCard);
                    }
                }
            }

        }
        catch (Exception e){
            logger.error("Error in building lpg, forex and cab cards map",e);
        }
        return cardsMap;
    }

    public Map<String, CardInfo> buildForexAndCabCard(Map<String, String> expDataMap, String cityCode, String cabsDeepLinkUrl, boolean bookingDeviceDesktop, boolean isThankyouV2) {
        Map<String, CardInfo> forexAndCabcardInfoMap = new HashMap<>();
        CardInfo forexAndCabCard = new CardInfo();
        CardPayloadData cardPayloadData = commonResponseTransformer.buildForexAndCabCardPayload(expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop, PAGE_CONTEXT_THANK_YOU);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(cardPayloadData.getGenericCardData())){
            return forexAndCabcardInfoMap;
        }
        forexAndCabCard.setCardPayload(cardPayloadData);
        String title;
        if(isThankyouV2){
            title = polyglotService.getTranslatedData(FOREX_CAB_CARD_TITLE_THANKYOU_PAGE_V2);
        }else{
            title = polyglotService.getTranslatedData(FOREX_CAB_CARD_TITLE_THANKYOU_PAGE);
        }
        forexAndCabCard.setTitleText(title);
        forexAndCabCard.setTemplateId(FOREX_CAB_CARD_TEMPLATE_ID);
        forexAndCabcardInfoMap.put(FOREX_CAB_CARD_ID,forexAndCabCard);
        return forexAndCabcardInfoMap;
    }

    /**
     * Builds DigiLocker card for easy check-in functionality
     * @return CardInfo object for DigiLocker card
     */
    public CardInfo buildDigiLockerCard(PersistedMultiRoomData persistedData, String bookingId) {
        try {
            CardInfo digiLockerCard = new CardInfo();
            
            // Set basic card properties
            digiLockerCard.setIconURL(digiLockerCardIconUrl);
            digiLockerCard.setTitleText(polyglotService.getTranslatedData(DIGILOCKER_CARD_TITLE));
            digiLockerCard.setActionText(polyglotService.getTranslatedData(DIGILOCKER_CARD_CTA));
            digiLockerCard.setBorderColor(DIGILOCKER_BORDER_COLOR);
            
            // Create and set card action with proper deeplink
            List<CardAction> cardActions = new ArrayList<>();
            CardAction cardAction = new CardAction();
            
            // Create query parameters map with booking ID and act parameter
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put(BOOKING_ID, bookingId);
            queryParams.put("act", "133");
            
            String digiLockerDeepLink = Utility.appendQueryParamsInUrl(
                getMytripsRawDeepLinkUrl(persistedData.getUserGlobalInfo(), persistedData.getExpData()), 
                queryParams
            );
            cardAction.setDeeplinkUrl(digiLockerDeepLink);
            cardActions.add(cardAction);
            digiLockerCard.setCardAction(cardActions);
            
            // Create and set background gradient
            BGLinearGradient bgLinearGradient = new BGLinearGradient();
            bgLinearGradient.setStart(DIGILOCKER_BG_START_COLOR);
            bgLinearGradient.setEnd(DIGILOCKER_BG_END_COLOR);
            bgLinearGradient.setCenter(DIGILOCKER_BG_CENTER_COLOR);
            bgLinearGradient.setDirection(DIGILOCKER_BG_DIRECTION);
            bgLinearGradient.setAngle(DIGILOCKER_BG_ANGLE);
            digiLockerCard.setBgLinearGradient(bgLinearGradient);
            
            return digiLockerCard;
            
        } catch (Exception e) {
            logger.error("Error in building DigiLocker card", e);
        }
        return null;
    }

    public void updateInsuranceInfo(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (persistedData.getAddOnInfo() != null && CollectionUtils.isNotEmpty(persistedData.getAddOnInfo().getAddOnNode())) {
            for (AddOnNode addOnNode : persistedData.getAddOnInfo().getAddOnNode()) {
                if (AddOnType.INSURANCE.name().equals(addOnNode.getAddOnType()) && thankYouResponse.getBookingDetails() != null) {
                    thankYouResponse.getBookingDetails().setBookingWithInsurance(true);
                    break;
                }
            }
        }
    }

    public void updateCurrencySymbol(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        String symbol = null;
        if (persistedData.getMultiCurrencyInfo() != null && persistedData.getMultiCurrencyInfo().getIsMultiCurrencyV2FlowEnabled()) {
            symbol = Currency.getCurrencyEnum(persistedData.getMultiCurrencyInfo().getUserCurrency()).getCurrencySymbol();
        }
        if (thankYouResponse != null && thankYouResponse.getPaidAmount() != null && StringUtils.isNotEmpty(symbol)) {
            thankYouResponse.getPaidAmount().setCurrencySymbol(symbol);
        }
        if (thankYouResponse != null && thankYouResponse.getTotalAmount() != null && StringUtils.isNotEmpty(symbol)) {
            thankYouResponse.getTotalAmount().setCurrencySymbol(symbol);
        }
    }

    public Map<String, CardInfo> buildLpgCardMap(PersistedMultiRoomData persistedData) {
        Map<String, CardInfo> cardInfoMap = null;
        try {
            if(persistedData != null && persistedData.getAvailReqBody() != null &&
                    !Utility.isRegionGccOrKsa(persistedData.getAvailReqBody().getSiteDomain()) && MapUtils.isNotEmpty(persistedData.getExpData()) &&
                    utility.isExperimentTrue(persistedData.getExpData(),lpgWelcomeOffer) && utility.isWelcomeMMTCouponApplied(persistedData.getCouponInfo())){
                cardInfoMap = new HashMap<>();
                CardInfo cardInfo = new CardInfo();
                BeanUtils.copyProperties(lgpWelcomeCardData, cardInfo);
                cardInfo.setTitleText(polyglotService.getTranslatedData(cardInfo.getTitleText()));
                cardInfo.setSubText(polyglotService.getTranslatedData(cardInfo.getSubText()));
                if(CollectionUtils.isNotEmpty(cardInfo.getCardAction())){
                    cardInfo.getCardAction().get(0).setTitle(polyglotService.getTranslatedData(WELCOME_OFFER_CARDACTION_TITLE));
                }
                cardInfoMap.put("mec", cardInfo);
            }else{
                boolean isPah = false, isBNPL = false;
                if (CollectionUtils.isNotEmpty(persistedData.getAmountLabels())) {
                    for (ThankYouAmountLabel thankYouAmountLabel : persistedData.getAmountLabels()) {
                        switch (thankYouAmountLabel.getLabelType()) {
                            case AMOUNT_LABEL_REMAINING_AMOUNT:
                                isBNPL = true;
                            case AMOUNT_LABEL_AMOUNT_HOTEL:
                                isPah = true;
                            default:
                                break;
                        }
                    }
                }
                cardInfoMap = new HashMap<>();
                logger.debug("Getting gcclpg card data gccLpgCardData for ThankYou page from properties: {}, isBNPL: {}, isPah: {}, gcclpg: {}"
                        ,gccLpgCardData, isBNPL, isPah, persistedData.getExpData()!=null?persistedData.getExpData().get("gcclpg"):persistedData.getExpData());
                if(!isPah && !isBNPL && Utility.isHotelGCCExclusive(getHotelCategories(persistedData)) && persistedData.getExpData() != null
                        && (persistedData.getExpData().get("gcclpg").trim().equals("2") || persistedData.getExpData().get("gcclpg").trim().equals("3"))) {
                    CardInfo cardInfo = gson.fromJson(gccLpgCardData, CardInfo.class);
                    cardInfo.setTitleText(polyglotService.getTranslatedData(cardInfo.getTitleText()));
                    cardInfo.setSubText(polyglotService.getTranslatedData(cardInfo.getSubText()));
                    if(CollectionUtils.isNotEmpty(cardInfo.getCardAction())){
                        cardInfo.getCardAction().get(0).setTitle(polyglotService.getTranslatedData(cardInfo.getCardAction().get(0).getTitle()));
                    }
                    cardInfoMap.put("mec", cardInfo);
                    logger.debug("Getting gccLpgCardData for ThankYou page cardsMap: {}",cardInfoMap);
                }
            }
        } catch (Exception e) {
            logger.error("Error while getting gccLpgCardData for ThankYou page from properties: ", e);
        }
        return cardInfoMap;
    }

    private Set<String> getHotelCategories(PersistedMultiRoomData persistedData){
        if(persistedData.getHotelList() != null && !persistedData.getHotelList().isEmpty()
                && persistedData.getHotelList().get(0).getHotelInfo()!=null
                && persistedData.getHotelList().get(0).getHotelInfo().getCategories() != null){
            return persistedData.getHotelList().get(0).getHotelInfo().getCategories();
        }
        return null;
    }

    private void buildTrackingMap(ThankYouResponse thankYouResponse, String key, String trackingKey) {
        if (MapUtils.isEmpty(thankYouResponse.getTrackingMap()))
            thankYouResponse.setTrackingMap(new HashMap<>());
        /*
         As per the client discussion we can not have more than 5 tracking keys in the tracking map.
         */
        if (thankYouResponse.getTrackingMap().size() <= TRACKING_MAP_THRESHOLD) {
            if (StringUtils.isNotEmpty(trackingKey)) {
                if (thankYouResponse.getTrackingMap().containsKey(key)) {
                    thankYouResponse.getTrackingMap().put(key, thankYouResponse.getTrackingMap().get(key).toString() + "|" + trackingKey);
                } else {
                    thankYouResponse.getTrackingMap().put(key, trackingKey);
                }
            }
        } else {
            logger.error("Tracking map size exceeded 5");
        }
    }

    private String appendPayModeAndMealPlanInTrackingText(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        // Extract booking ID and update tracking text with payment info if available
        final StringBuilder payModeTrackingText = new StringBuilder();
        Optional<String> bookingIdOpt = getBookingId(thankYouResponse);
        bookingIdOpt.flatMap(bookingId -> getPaymentType(persistedData, bookingId)).ifPresent(paymentType -> {
            payModeTrackingText.append("PayMode_").append(paymentType);
        });

        // Update tracking text with meal plan info if available
        final StringBuilder mealPlanTrackingText = new StringBuilder();
        getTariffInfoList(persistedData).ifPresent(tariffInfoList -> {
            mealPlanTrackingText.append(generateMealPlanTrackingText(tariffInfoList));
        });
        if (StringUtils.isNotEmpty(payModeTrackingText.toString()) && StringUtils.isNotEmpty(mealPlanTrackingText.toString())) {
            return payModeTrackingText.toString() + "|" + mealPlanTrackingText.toString();
        } else if (StringUtils.isNotEmpty(payModeTrackingText.toString())) {
            return payModeTrackingText.toString();
        } else if (StringUtils.isNotEmpty(mealPlanTrackingText.toString())) {
            return mealPlanTrackingText.toString();
        }
        return "";
    }

    private Optional<String> getBookingId(ThankYouResponse thankYouResponse) {
        return Optional.ofNullable(thankYouResponse.getBookingDetails())
                .map(BookingDetails::getBookingId)
                .filter(StringUtils::isNotEmpty);
    }

    private Optional<String> getPaymentType(PersistedMultiRoomData persistedData, String bookingId) {
        return Optional.ofNullable(persistedData.getLobPaymentInfo())
                .map(lobPaymentInfo -> lobPaymentInfo.get("HOTEL"))
                .filter(MapUtils::isNotEmpty)
                .map(hotelMap -> hotelMap.get(bookingId))
                .filter(CollectionUtils::isNotEmpty)
                .map(paymentInfoList -> paymentInfoList.get(0))
                .filter(Objects::nonNull)
                .map(PaymentData::getType)
                .filter(StringUtils::isNotEmpty);
    }

    private String updateTrackingText(String currentText, String newText) {
        return StringUtils.isEmpty(currentText) ? newText : concatText(currentText, newText);
    }

    private Optional<List<PersistedTariffInfo>> getTariffInfoList(PersistedMultiRoomData persistedData) {
        return Optional.ofNullable(persistedData.getHotelList())
                .filter(CollectionUtils::isNotEmpty)
                .map(hotelList -> hotelList.get(0))
                .filter(Objects::nonNull)
                .map(PersistedHotel::getTariffInfoList)
                .filter(CollectionUtils::isNotEmpty);
    }

    private String generateMealPlanTrackingText(List<PersistedTariffInfo> tariffInfoList) {
        boolean multipleMealPlansAvailable = tariffInfoList.size() > 1;
        StringBuilder mealPlanTrackingText = new StringBuilder();

        IntStream.range(0, tariffInfoList.size())
                .mapToObj(i -> createMealPlanText(tariffInfoList.get(i), multipleMealPlansAvailable, i + 1))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(mealPlanText -> {
                    if (mealPlanTrackingText.length() > 0) {
                        mealPlanTrackingText.append(PIPE);
                    }
                    mealPlanTrackingText.append(mealPlanText);
                });

        return mealPlanTrackingText.toString();
    }

    private Optional<String> createMealPlanText(PersistedTariffInfo tariffInfo, boolean multipleMealPlansAvailable, int counter) {
        return Optional.ofNullable(tariffInfo.getMealPlans())
                .filter(CollectionUtils::isNotEmpty)
                .map(mealPlans -> mealPlans.get(0))
                .filter(Objects::nonNull)
                .map(MealPlan::getCode)
                .filter(StringUtils::isNotEmpty)
                .map(code -> multipleMealPlansAvailable ? "Meal" + counter + "_" + code : "Meal_" + code);
    }


    private String buildCabsDeepLinkUrl(PersistedMultiRoomData persistedMultiRoomData) {
        if (persistedMultiRoomData != null && persistedMultiRoomData.getBookingMetaInfo() != null && StringUtils.isNotEmpty(persistedMultiRoomData.getCabsDeepLinkUrl())) {
            String bookingId = persistedMultiRoomData.getBookingMetaInfo() != null
                    && StringUtils.isNotEmpty(persistedMultiRoomData.getBookingMetaInfo().getBookingId())
                    ? persistedMultiRoomData.getBookingMetaInfo().getBookingId() : EMPTY_STRING;
            String cabsDeepLinkUrl = persistedMultiRoomData.getCabsDeepLinkUrl();
            cabsDeepLinkUrl = cabsDeepLinkUrl + bookingId;
            return cabsDeepLinkUrl;
        }
        return null;
    }

    private String concatText(String trackingText, String trackingType) {
        if (org.apache.commons.lang.StringUtils.isEmpty(trackingText)) {
            return trackingType;
        } else {
            return trackingText + PIPE + trackingType;
        }
    }

    public Coupon buildBenefitDeals(BestCoupon couponInfo, CouponInfo appliedCouponInfo, List<String> hydraSegments, String cabsDeepLinkUrl, boolean isCashBackCard) {
        if(couponInfo == null || couponInfo.getForexCouponDetails() == null)
            return null;
        Coupon benefitDeal = new Coupon();
        if(CollectionUtils.isNotEmpty(hydraSegments) && CollectionUtils.isNotEmpty(flightsBookerHydraSegment) &&
                utility.hasCommonElements(hydraSegments, flightsBookerHydraSegment)){
            benefitDeal.setIconUrl(forexIconUrl);
        }

        String forexTitle = polyglotService.getTranslatedData(FOREX_DEAL_TITLE_THANKU_PAGE);
        if(StringUtils.isNotEmpty(forexTitle)) {
            benefitDeal.setTitle(MessageFormat.format(forexTitle, String.valueOf((int) couponInfo.getForexCashbackAmount())));
        }
        benefitDeal.setDescription(couponInfo.getDescription());
        if (appliedCouponInfo != null && StringUtils.isNotEmpty(appliedCouponInfo.getSuccessApplyMessage())) {
            benefitDeal.setDescription(appliedCouponInfo.getSuccessApplyMessage());
        }
        benefitDeal.setPersuasionText(couponInfo.getForexCouponDetails().getPersuasion_message());
        benefitDeal.setTncUrl(couponInfo.getTncUrl());
        benefitDeal.setTncText(OFFER_AND_TERMS);
        benefitDeal.setPromoIcon(forexPromoIconUrl);
        benefitDeal.setBgLinearGradient(utility.buildBgLinearGradientforForex());
        if(benefitDeal.getBgLinearGradient() != null){
            benefitDeal.getBgLinearGradient().setEnd(GRADIENT_END_V2);
        }
        Cta cta = new Cta();
        if (FOREX_ANCILLARY_NAME_FLIGHTS.equalsIgnoreCase(couponInfo.getForexCouponDetails().getAncillaryName())) {
            cta.setTitle(BUY_FOREX_NOW);
            cta.setDeeplink(getDeeplinkUrl(forexDeeplinkUrlApps, forexDeeplinkUrlDtPwa));
        } else if(FOREX_ANCILLARY_NAME_CABS.equalsIgnoreCase(couponInfo.getForexCouponDetails().getAncillaryName())){
            cta.setTitle(BOOK_CAB_NOW);
            cta.setDeeplink(StringUtils.isNotEmpty(cabsDeepLinkUrl) ? cabsDeepLinkUrl : getDeeplinkUrl(cabsforexDeeplinkUrlApps, cabsforexDeeplinkUrlDtPwa));
        }
        benefitDeal.setCta(cta);
        if(utility.hasCommonElements(hydraSegments,intlFlyerHydraSegmentIds)){
            benefitDeal.setIconTags(utility.buildIntlFlyerIconTag());
        }
        if(isCashBackCard){
            benefitDeal.setUiIdentifier(CASHBACK_IDENTIFIER);
            benefitDeal.setIconUrl(cabCashbackIconUrl);
            benefitDeal.setTncUrl(cabWebViewUrl);
            BGLinearGradient bgLinearGradient = new BGLinearGradient();
            bgLinearGradient.setStart(FFFFFF);
            bgLinearGradient.setEnd(FAF2E4);
            bgLinearGradient.setDirection(DIRECTION_DIAGONAL);
            benefitDeal.setBgLinearGradient(bgLinearGradient);
            if (FOREX_ANCILLARY_NAME_FLIGHTS.equalsIgnoreCase(couponInfo.getForexCouponDetails().getAncillaryName())) {
                benefitDeal.setIconUrl(forexCashbackIconUrl);
                benefitDeal.setTncUrl(forexWebViewUrl);
            } else if(FOREX_ANCILLARY_NAME_CABS.equalsIgnoreCase(couponInfo.getForexCouponDetails().getAncillaryName())){
                benefitDeal.setIconUrl(cabCashbackIconUrl);
                benefitDeal.setTncUrl(cabWebViewUrl);
            }
        }
        return benefitDeal;
    }

    private String getDeeplinkUrl(String appUrl, String webUrl) {
        String clientType = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
        if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
            return appUrl;
        } else {
            return webUrl;
        }
    }


    /**
     * Method to Prepare CommonModifierResponse object from txn data.
     * @param txnData Transaction Data fetched from HES.
     * @return CommonModifierResponse object.
     */
    public CommonModifierResponse buildCommonModifierFromTxnData(PersistedMultiRoomData txnData) {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        if (txnData != null) {
            //Prepare Hydra service response object from txn data
            HydraResponse hydraResponse = new HydraResponse();
            hydraResponse.setHydraMatchedSegment(txnData.getHydraSegments() != null ? new HashSet<>(Arrays.asList(txnData.getHydraSegments())) : new HashSet<>());
            commonModifierResponse.setHydraResponse(hydraResponse);
            commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
            if (MapUtils.isNotEmpty(txnData.getExpData())) {
                txnData.getExpData().forEach((expKey, expValue) -> commonModifierResponse.getExpDataMap().put(expKey, expValue));
            }
            //Prepare user service response object from txn data
            ExtendedUser extendedUser = new ExtendedUser();
            extendedUser.setUuid(txnData.getAvailReqBody() != null ? txnData.getAvailReqBody().getUuid() : "");
            commonModifierResponse.setExtendedUser(extendedUser);
        }
        return commonModifierResponse;
    }

    private void buildMyPartnerFareHoldData(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {

        logger.debug("buildMyPartnerFareHoldData for thankYouResponse{} ", thankYouResponse);
        //building Complete Payment Card for BookNowFareHold if booking status is succcess and user is eligible for booking
        boolean isMpFareHoldFlow = persistedMultiRoomData.isBookNowFareHold() && persistedMultiRoomData.getMpaFareHoldStatus()!=null &&
                persistedMultiRoomData.getMpaFareHoldStatus().isHoldEligible() && persistedMultiRoomData.getMpaFareHoldStatus().isEligibleForHoldBooking();

        if (isMpFareHoldFlow && BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {

            thankYouResponse.getBookingDetails().setCompletePaymentCard(new CompletePaymentCard());
            List<AmountDetail> amountBreakup = null;
            Long expiry = persistedMultiRoomData.getMpaFareHoldStatus().getExpiry();
            final String expriryTime = persistedMultiRoomData.getMpaFareHoldStatus().getExpiryTime();
            //In case of success booking there can be a case when we have already built success card but in case of fare hold
            //we need to show only complete payment card and need to built amount break up for that
            //If success card is available fetch amount breakup from it else building the data
            //set success card to null

            amountBreakup = buildAmountBreakup(thankYouResponse, persistedMultiRoomData);

            AmountDetail pendingAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(AMOUNT_BREAKUP_PENDING).equalsIgnoreCase(amountDetail.getTitle()))
                    .findFirst().orElse(null);
            if(expiry==null || pendingAmount==null) {
                logger.error("MPFAREHOLD Either expiry or pending amount is null on thank-yopu page");
                logger.debug("Pending Amount {}  Expiry {} ", pendingAmount, expiry);
                return;
            }
            //there can be a case when e have already set successBookingCardInfo in the flow as Booking Statuis is Success hence making it null as only one card is required
            thankYouResponse.getBookingDetails().setSuccessBookingCardInfo(null);

            thankYouResponse.getBookingDetails().getCompletePaymentCard().setAmountBreakup(amountBreakup);
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setHeading(polyglotService.getTranslatedData(BOOK_NOW_THANK_YOU_HEADING));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setSubHeading(MessageFormat.format(
                    polyglotService.getTranslatedData(BOOK_NOW_THANK_YOU_SUB_HEADING),String.format("%,.2f",pendingAmount.getAmount()),
                    expriryTime));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setPaymentHeading(polyglotService.getTranslatedData(BOOK_NOW_THANK_YOU_PAYMENT_HEADING));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setCtaText(MessageFormat.format(
                    polyglotService.getTranslatedData(BOOK_NOW_THANK_YOU_CTA_TEXT),String.format("%,.2f",pendingAmount.getAmount())));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedMultiRoomData.getUserGlobalInfo(), persistedMultiRoomData.getExpData()), persistedMultiRoomData.getBookingMetaInfo()));
            thankYouResponse.getBookingDetails().getCompletePaymentCard().setExpiry(expiry);
            //Building paid amount as equal to booking amount
            if (thankYouResponse.getPaidAmount() != null && !persistedMultiRoomData.isMyPartnerMoveToTdsTaxStructure()) {
                thankYouResponse.getPaidAmount().setAmount((double) persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount());
            } else {
                AmountDetail partialAmountPaid = null;
                partialAmountPaid = new AmountDetail();
                partialAmountPaid.setTitle(AMOUNT_PAID);
                partialAmountPaid.setAmount((double) persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount());
                partialAmountPaid.setCurrency(DEFAULT_CUR_INR); //MyPartner is only for Indian funnel
            }
        }
    }
    private void buildGroupBookingSpecificNodes(HotelResult hotelResult, PersistedMultiRoomData persistedData, HotelInfo hotelInfo) {
        if (isGroupBookingFunnel(persistedData)) {
            hotelResult.setGroupBookingHotel(hotelInfo.isGroupBookingHotel());
            hotelResult.setGroupBookingPrice(hotelInfo.isGroupBookingPrice());
            if (persistedData.getTotalDisplayFare() != null && persistedData.getTotalDisplayFare().getTotalRoomCount() != null &&
                    CollectionUtils.isNotEmpty(persistedData.getHotelList().get(0).getTariffInfoList()) && persistedData.getHotelList().get(0).getTariffInfoList().stream().anyMatch(PersistedTariffInfo::isBaseRoom)) {
                Integer roomCount = persistedData.getTotalDisplayFare().getTotalRoomCount();
                String roomName = persistedData.getHotelList().get(0).getTariffInfoList().stream().filter(PersistedTariffInfo::isBaseRoom).findFirst().get().getRoomTypeName();
                String roomText = roomCount.toString() + SPACE_X_SPACE + roomName;
                hotelResult.setRoomText(roomText);
            }
        }
    }

    private void buildPendingAmount(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail pendingAmount = null;
        Optional<ThankYouAmountLabel> totalPendingAmount = Optional.empty();
        if(persistedMultiRoomData.getAmountLabels() != null){
            totalPendingAmount = persistedMultiRoomData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                    AMOUNT_LABEL_PARTIAL_AMOUNT_LEFT.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        }

        if (totalPendingAmount.isPresent()) {
            boolean isMyPartnerRequest = Objects.nonNull(persistedMultiRoomData.getAvailReqBody()) && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType());
            double pgCharges = 0;
            if (persistedMultiRoomData.getPaymentInfo() != null && !isMyPartnerRequest) {
                double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
                pgCharges = persistedMultiRoomData.getPaymentInfo().getPgCharges();
                pgCharges /= conversionFactor;
            }
            pendingAmount = new AmountDetail();
            pendingAmount.setTitle(PENDING_AMOUNT);
            pendingAmount.setAmount(Utility.round(Math.round(totalPendingAmount.get().getAmount() + pgCharges), 0));
            pendingAmount.setCurrency(totalPendingAmount.get().getAmountText().split("\\s+")[1]);
            pendingAmount.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedMultiRoomData.getUserGlobalInfo(), persistedMultiRoomData.getExpData()), persistedMultiRoomData.getBookingMetaInfo()));
            thankYouResponse.setPendingAmount(pendingAmount);
            updatePgChargesInPaidAmount(thankYouResponse.getPendingAmount(), persistedMultiRoomData);
        }
    }

    private AmountDetail buildPartialAmountPaid(PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail partialAmountPaid = null;
        Optional<ThankYouAmountLabel> partialAmountCollected = persistedMultiRoomData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                AMOUNT_LABEL_PARTIAL_AMOUNT_PAID.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        if (partialAmountCollected.isPresent()) {
            boolean isMyPartnerRequest = Objects.nonNull(persistedMultiRoomData.getAvailReqBody()) && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType());
            double pgCharges = 0;
            if (persistedMultiRoomData.getPaymentInfo() != null && !isMyPartnerRequest) {
                double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
                pgCharges = persistedMultiRoomData.getPaymentInfo().getPgCharges();
                pgCharges /= conversionFactor;
            }
            partialAmountPaid = new AmountDetail();
            partialAmountPaid.setTitle(AMOUNT_PAID);
            partialAmountPaid.setAmount(Utility.round(Math.round(partialAmountCollected.get().getAmount() + pgCharges), 0));
            partialAmountPaid.setCurrency(partialAmountCollected.get().getAmountText().split("\\s+")[1]);
        }
        return partialAmountPaid;
    }

    private void buildBPGText(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList()) && persistedMultiRoomData.getHotelList().get(0).getHotelInfo() != null &&
                persistedMultiRoomData.getHotelList().get(0).getHotelInfo().isGroupBookingHotel() && thankYouResponse.getRooms() != null) {
            thankYouResponse.getRooms().setBpgText(BEST_PRICE_GUARANTEE);
        }
    }

    private void updatePgChargesInPaidAmount(AmountDetail paidAmount, PersistedMultiRoomData persistedData) {
        if (paidAmount != null && persistedData.getPaymentInfo() != null) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedData);
            double pgCharges = persistedData.getPaymentInfo().getPgCharges();
            pgCharges /= conversionFactor;
            if (pgCharges > 0.0) {
                String currSign = paidAmount.getCurrency().equalsIgnoreCase("INR") ? "Rs" : paidAmount.getCurrency();
                Integer roundedPgCharges = (int) Math.round(pgCharges);
                paidAmount.setSubtitle(polyglotService.getTranslatedData(PG_CHARGES_TEXT).replace("{currency}", currSign).replace("{amount}", roundedPgCharges.toString()));
            }
        }

    }

    private List<InsuranceInfo> buildInsuranceInfo(PersistedMultiRoomData persistedData) {
        AddOnDetail addOnDetail = persistedData.getAddOnInfo();
        List<InsuranceInfo> list = new ArrayList<>();
        if (addOnDetail != null && CollectionUtils.isNotEmpty(addOnDetail.getAddOnNode())) {
            Optional<AddOnNode> insuranceAddOn = addOnDetail.getAddOnNode().stream().filter(e -> AddOnType.INSURANCE.toString().equalsIgnoreCase(e.getAddOnType())).findFirst();
            if (insuranceAddOn.isPresent()) {
                AddOnNode addOnNode = insuranceAddOn.get();
                if (addOnNode.getInsuranceData() != null && CollectionUtils.isNotEmpty(addOnNode.getInsuranceData().getTmInsuranceAddOns())) {
                    for (TmInsuranceAddOns insurance : addOnNode.getInsuranceData().getTmInsuranceAddOns()) {
                        if (StringUtils.isNotBlank(insurance.getTmProBookingId())) {
                            InsuranceInfo i = new InsuranceInfo();
                            i.setHeaderText(insurance.getHeading());
                            //i.setIconUrl(insurance.getLargeIcon());
                            String thankYouPageInsuranceSubTextIcon = null;
                            String thankYouPageInsuranceSubText = null;
                            if (persistedData.getInsuranceDataOptions() != null) {
                                thankYouPageInsuranceSubTextIcon = persistedData.getInsuranceDataOptions().getThankYouPageInsuranceSubTextIcon();
                                thankYouPageInsuranceSubText = persistedData.getInsuranceDataOptions().getThankYouPageInsuranceSubText();
                            }
                            if (StringUtils.isNotEmpty(thankYouPageInsuranceSubTextIcon))
                                i.setIconUrl(thankYouPageInsuranceSubTextIcon);
                            else
                                i.setIconUrl("https://promos.makemytrip.com/Hotels_product/Review/IncIcon.png");
                            if (StringUtils.isNotEmpty(thankYouPageInsuranceSubText))
                                i.setSubText(thankYouPageInsuranceSubText);
                            else if(persistedData.isBnplZeroVariantBooking()){
                                i.setSubText(polyglotService.getTranslatedData(THANkYOU_INSURANCE_SUBTEXT_BNPL));
                            }
                            else
                                i.setSubText(polyglotService.getTranslatedData(THANkYOU_INSURANCE_SUBTEXT));
                            i.setViewAllText(insurance.getTncText());
                            i.setViewAllUrl(insurance.getTncLink());
                            list.add(i);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list))
            return list;
        return null;
    }

    private  void buildRtbChatCard(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData){
        if(persistedData.isRequestToBook() && !persistedData.isRtbPreApproved()){
            RtbChatCard rtbChatCard = new RtbChatCard();
            rtbChatCard.setTitle(polyglotService.getTranslatedData(RTB_CHAT_CARD_TITLE));
            rtbChatCard.setSubTitle(polyglotService.getTranslatedData(RTB_CHAT_CARD_SUBTITLE));
            rtbChatCard.setActionText(polyglotService.getTranslatedData(RTB_CHAT_CARD_ACTION_TEXT));
            rtbChatCard.setIngoHotelId(persistedData.getHotelList().get(0).getHotelInfo().getGdsHotelCode());
            thankYouResponse.setRtbChatCard(rtbChatCard);
        }
    }

    private void buildMetaChannelInfo(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (persistedData == null || persistedData.getAvailReqBody() == null || persistedData.getAvailReqBody()
                                                                                             .getMetaChannelInfo() == null) {
            return;
        }
        thankYouResponse.setMetaChannelInfo(persistedData.getAvailReqBody().getMetaChannelInfo());
    }

    private void buildLogData(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData){

        try{
            if(persistedData!= null ){
                Map<String,String> logData = new HashMap<>();
                thankYouResponse.setLogData(logData);
                if( persistedData.getTotalDisplayFare()!= null && persistedData.getTotalDisplayFare().getDisplayPriceBreakDown()!= null) {
                    logData.put("hotelierDiscount", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getMmtDiscount()));
                    logData.put("hotelierServiceCharge", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getHotelTax()));
                    logData.put("wallet", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getWallet()));
                    logData.put("mmtServiceCharge", Double.toString(persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getMmtServiceCharge()));

                }

                if(MapUtils.isNotEmpty(persistedData.getCouponInfo()) && persistedData.getCouponInfo().containsKey(CouponStatus.REDEEMED) && CollectionUtils.isNotEmpty(persistedData.getCouponInfo().get(CouponStatus.REDEEMED))){
                    logData.put("couponDiscount", persistedData.getCouponInfo().get(CouponStatus.REDEEMED).get(0).getAmount());
                }

                if(CollectionUtils.isNotEmpty(persistedData.getTravelerInfoList()) ){
                    logData.put("travellerIsdCode", persistedData.getTravelerInfoList().get(0).getIsdCode());
                }

            }
        }catch (Exception ex){
            logger.error("Error in populating error data : {}", ex.getMessage(), ex);
        }
    }

    private void buildCardDetails(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        if (MapUtils.isEmpty(thankYouCards))
            return;
        List<CardData> thankYouCardList = new ArrayList<>();
        for (String cardId : thankYouCards.keySet()) {
            CardData thankyouCard = gson.fromJson(gson.toJson(thankYouCards.get(cardId)), CardData.class);

            if(StringUtils.isNotBlank(persistedData.getSpiderNextBookingDiscountMessage())) {
                if (cardId.equalsIgnoreCase("NEXTBOOKINGDISCOUNT")){
                    thankyouCard.getCardInfo().setTitleText(persistedData.getSpiderNextBookingDiscountMessage());
                    if (null == persistedData.getAvailReqBody().getTrafficSource()) {
                        thankyouCard.getCardInfo().getCardPayload().setMetaPersuasion(null);
                        thankyouCard.getCardInfo().getCardPayload().setTitle(null);
                        thankYouCardList.add(thankyouCard);
                    }
                } else if (cardId.equalsIgnoreCase("NEXTBOOKINGSCRATCH")) {
                    thankyouCard.getCardInfo().getCardPayload().setScratchText(persistedData.getSpiderNextBookingDiscountMessage());
                    thankYouCardList.add(thankyouCard);
                }
            }
            boolean isMyPartnerRequest = (persistedData.getAvailReqBody()!=null) && Utility.isMyPartnerRequest(persistedData.getAvailReqBody().getProfileType(),persistedData.getAvailReqBody().getSubProfileType());
            if (isGroupBookingFunnel(persistedData) && groupBookingCardKeys.contains(cardId) && !isMyPartnerRequest) {
                thankYouCardList.add(thankyouCard);
            }

            if (isGroupBookingFunnel(persistedData) && isMyPartnerRequest && groupBookingCardKeys.contains(cardId) && !POST_BOOKING_CARD.equalsIgnoreCase(cardId)) {
                thankYouCardList.add(thankyouCard);
            }

            if(CollectionUtils.isNotEmpty(thankYouCardList)) {
                thankYouResponse.setCardData(thankYouCardList);
            }
        }
    }

    private boolean isGroupBookingFunnel(PersistedMultiRoomData persistedData) {
        return persistedData.getAvailReqBody() != null && StringUtils.equalsIgnoreCase(persistedData.getAvailReqBody().getFunnelSource(), FUNNEL_SOURCE_GROUP_BOOKING);
    }

    private void updateInclusions(ThankYouResponse thankYouResponse, boolean isAllInclusion) {
        if (thankYouResponse != null && thankYouResponse.getRooms() != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(thankYouResponse.getRooms().getRatePlanList()) && !isAllInclusion) {
            for (BookedRatePlan ratePlan : thankYouResponse.getRooms().getRatePlanList()) {
                List<BookedInclusion> inclusions = ratePlan.getInclusions();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions) && inclusions.size() > maxInclusionsThankyou) {
                    inclusions.sort(Comparator.comparing(i -> i.getSegmentIdentifier(), Comparator.nullsLast(Comparator.naturalOrder())));
                    inclusions = inclusions.subList(0, maxInclusionsThankyou);
                    ratePlan.setInclusions(inclusions);
                }
            }
        }
    }

    private Map<String, String> buildExperimentData(PersistedMultiRoomData persistedData) {
        if (MapUtils.isNotEmpty(persistedData.getExpData()))
            return persistedData.getExpData();
        return null;
    }

    private void populateAmountBreakup(ThankYouResponse thankYouResponse, PersistanceMultiRoomResponseEntity persistanceMultiRoomResponseEntity) {

        List<AmountDetail> amountBreakup = buildAmountBreakup(thankYouResponse, persistanceMultiRoomResponseEntity.getPersistedData());
        AmountDetail totalAmount = null;
        if (CollectionUtils.isNotEmpty(amountBreakup))
            totalAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(AMOUNT_BREAKUP_TOTAL).equalsIgnoreCase(amountDetail.getTitle()))
                    .findFirst().orElse(null);
        if (thankYouResponse.getTotalAmount() == null && totalAmount != null)
            thankYouResponse.setTotalAmount(totalAmount);
        if (BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {
            thankYouResponse.getBookingDetails().setSuccessBookingCardInfo(new SuccessBookingCardInfo());
            thankYouResponse.getBookingDetails().getSuccessBookingCardInfo().setAmountBreakup(amountBreakup);
        }
        AmountDetail deducedAmount = null;
        if(amountBreakup != null)
            deducedAmount = amountBreakup.stream().filter(amountDetail -> polyglotService.getTranslatedData(AMOUNT_BREAKUP_DEDUCTED).equalsIgnoreCase(amountDetail.getTitle()))
                .findFirst().orElse(null);
        if (null != deducedAmount)
            thankYouResponse.setPaidAmount(deducedAmount);
    }

    private int getUniqueCheckinCheckoutCombinationsCount(PersistedMultiRoomData persistedData) {
        Set<String> uniqueCombinations = new HashSet<>();
        
        if (persistedData != null && 
            CollectionUtils.isNotEmpty(persistedData.getHotelList()) && 
            persistedData.getHotelList().get(0) != null &&
            CollectionUtils.isNotEmpty(persistedData.getHotelList().get(0).getTariffInfoList())) {
            
            for (PersistedTariffInfo tariffInfo : persistedData.getHotelList().get(0).getTariffInfoList()) {
                if (tariffInfo.getStayDetails() != null) {
                    String checkin = DEFAULT_DATE_TEXT;
                    String checkout = DEFAULT_DATE_TEXT;
                    
                    if (tariffInfo.getStayDetails().getCheckIn() != null) {
                        try {
                            checkin = dateUtil.format(tariffInfo.getStayDetails().getCheckIn(), DateUtil.YYYY_MM_DD);
                        } catch (Exception e) {
                            logger.error("Error formatting checkin date: {}", e.getMessage(), e);
                            checkin = DEFAULT_DATE_TEXT;
                        }
                    }
                    
                    if (tariffInfo.getStayDetails().getCheckOut() != null) {
                        try {
                            checkout = dateUtil.format(tariffInfo.getStayDetails().getCheckOut(), DateUtil.YYYY_MM_DD);
                        } catch (Exception e) {
                            logger.error("Error formatting checkout date: {}", e.getMessage(), e);
                            checkout = DEFAULT_DATE_TEXT;
                        }
                    }
                    
                    String combination = checkin + "|" + checkout;
                    uniqueCombinations.add(combination);
                }
            }
        }
        
        return uniqueCombinations.size();
    }

    private BookedRooms buildBookedRooms(PersistedMultiRoomData persistedData, ThankYouResponse thankYouResponse, Map<String, String> expDataMap) {
        BookedRooms rooms = new BookedRooms();
        Map<String, Integer> roomBedCountMap = new HashMap<>();
        roomBedCountMap.put(SELLABLE_ROOM_TYPE,0);
        roomBedCountMap.put(SELLABLE_BED_TYPE,0);
        rooms.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedData.getUserGlobalInfo(), persistedData.getExpData()), persistedData.getBookingMetaInfo()));
        boolean isExtraAdultChildInclusion = Boolean.TRUE.equals(persistedData.getIsExtraAdultChild());
        List<BookedRatePlan> ratePlanList = persistedData.getHotelList().get(0).getTariffInfoList().stream()
                .map(persistedTariffInfo -> {
                    BookedRatePlan bookedRatePlan = new BookedRatePlan();
                    bookedRatePlan.setRoomName(persistedTariffInfo.getRoomTypeName());

                    //populate room occupancy
                    int adultCount = 0, childCount = 0, roomCount = 0;
                    List<String> childAges = new ArrayList<>();
                    BookedChild bookedChild = null;
                    if (CollectionUtils.isNotEmpty(persistedTariffInfo.getStayDetails().getRoomStayCandidates())) {
                        for (RoomStayCandidateReqBody roomStayCandidate : persistedTariffInfo.getStayDetails().getRoomStayCandidates()) {
                            roomCount++;
                            adultCount += roomStayCandidate.getAdult();
                            if (roomStayCandidate.getChild() != null) {
                                if (null != roomStayCandidate.getChild().getCount())
                                    childCount += roomStayCandidate.getChild().getCount();
                                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChild().getAges()))
                                    childAges.addAll(roomStayCandidate.getChild().getAges().stream().map(String::valueOf).collect(Collectors.toList()));
                            }
                        }
                    }
                    if (childCount > 0) {
                        bookedChild = new BookedChild();
                        bookedChild.setCount(childCount);
                        bookedChild.setAges(childAges);
                    }
                    BookedOccupancy occupancy = new BookedOccupancy();
                    occupancy.setAdult(adultCount);
                    if (null != bookedChild)
                        occupancy.setChild(bookedChild);

                    bookedRatePlan.setOccupancy(occupancy);
                    bookedRatePlan.setRoomCount(roomCount);

                    boolean isBnplOneVariant = persistedData.isBnplNewVariantBooking();
                    boolean isBnplZeroVariant = persistedData.isBnplZeroVariantBooking();
                    boolean isThankyouV2 = isThankyouV2(expDataMap);
                    if (isBnplZeroVariant) {
                        bookedRatePlan.setCancellationPolicy(buildCancellationPolicy(persistedTariffInfo, isBnplOneVariant, BNPLVariant.BNPL_AT_0, isThankyouV2));
                    } else if (isBnplOneVariant) {
                        bookedRatePlan.setCancellationPolicy(buildCancellationPolicy(persistedTariffInfo, isBnplOneVariant, BNPLVariant.BNPL_AT_1, isThankyouV2));
                    } else {
                        bookedRatePlan.setCancellationPolicy(buildCancellationPolicy(persistedTariffInfo, isBnplOneVariant, null, isThankyouV2));
                        modifyCancellationPolicyForFlexiCancel(persistedData, bookedRatePlan);
                    }
                    bookedRatePlan.setInclusions(buildInclusions(
                            persistedTariffInfo, persistedData.getHotelList().get(0).getHotelInfo(),
                            persistedData.getExpData(),isExtraAdultChildInclusion));
                    if (SELLABLE_BED_TYPE.equalsIgnoreCase(persistedTariffInfo.getSellableType())) {
                        roomBedCountMap.put(SELLABLE_BED_TYPE, roomBedCountMap.get(SELLABLE_BED_TYPE) + roomCount);
                    } else {
                        roomBedCountMap.put(SELLABLE_ROOM_TYPE, roomBedCountMap.get(SELLABLE_ROOM_TYPE) + roomCount);
                    }

                    if (CORP_ID_CONTEXT.equalsIgnoreCase(persistedData.getAvailReqBody().getIdContext()) && persistedTariffInfo.getDisplayFare() != null && persistedTariffInfo.getDisplayFare().getCorpMetaData() != null) {
                        bookedRatePlan.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(persistedTariffInfo.getDisplayFare().getCorpMetaData(), utility.isTCSV2FlowEnabled(persistedData.getExpData())));
                        bookedRatePlan.setCorpRateTags(commonResponseTransformer.buildTags(persistedTariffInfo.getDisplayFare().getCorpMetaData().getTags()));
                    }
                    bookedRatePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(persistedTariffInfo.getCancellationTimeline(), false, null));
                    bookedRatePlan.setBasePlan(persistedTariffInfo.isBaseRoom());
                    if(StringUtils.isNotEmpty(persistedData.getHighlightOccassionImageUrl())){
                        bookedRatePlan.setHighlightImage(persistedData.getHighlightOccassionImageUrl());
                    } else if(persistedData.isPackageRate()) {
                        bookedRatePlan.setHighlightImage(superPackageIconUrl);
                    }
                    int numberOfRooms = (int)persistedData.getHotelList().get(0).getTariffInfoList().stream().count();
                    if(persistedTariffInfo.getStayDetails()!= null && persistedTariffInfo.getStayDetails().getCheckIn() != null && persistedTariffInfo.getStayDetails().getCheckOut() != null) {
                        try {
                            bookedRatePlan.setCheckin(dateUtil.format(persistedTariffInfo.getStayDetails().getCheckIn(), DateUtil.YYYY_MM_DD));
                            bookedRatePlan.setCheckout(dateUtil.format(persistedTariffInfo.getStayDetails().getCheckOut(), DateUtil.YYYY_MM_DD));
                        } catch (Exception e) {
                            logger.error("Error formatting dates for booking", e.getMessage(), e);
                        }
                   }
                    if(bookedRatePlan.getCheckout() != null && StringUtils.isNotEmpty(bookedRatePlan.getCheckout()) && numberOfRooms > 1) {
                        String formattedDate = dateUtil.getDateFormatted(bookedRatePlan.getCheckout(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY);
                        PersuasionResponse persuasionResponse = commonResponseTransformer.buildRoomChangePersuasion(formattedDate, numberOfRooms);
                        bookedRatePlan.setRoomChangeDetails(persuasionResponse);
                    }

                    return bookedRatePlan;
                }).collect(Collectors.toList());
        rooms.setRatePlanList(ratePlanList);
        if (CollectionUtils.isNotEmpty(ratePlanList) && thankYouResponse.getHotelDetails() != null) {
            int totalRooms = ratePlanList.stream().mapToInt(rp -> rp.getRoomCount()).sum();
            if(roomBedCountMap.get(SELLABLE_ROOM_TYPE) != null && roomBedCountMap.get(SELLABLE_ROOM_TYPE) > 0){
                int uniqueCheckinCheckoutCombinationsCount = getUniqueCheckinCheckoutCombinationsCount(persistedData);
                int updatedTotalRooms = uniqueCheckinCheckoutCombinationsCount > 0 ? roomBedCountMap.get(SELLABLE_ROOM_TYPE) / uniqueCheckinCheckoutCombinationsCount : roomBedCountMap.get(SELLABLE_ROOM_TYPE);
                roomBedCountMap.put(SELLABLE_ROOM_TYPE, updatedTotalRooms);
            }
            setSellableTypeAndEntirePropertyText(persistedData, thankYouResponse, roomBedCountMap, totalRooms, expDataMap);

        }
        return rooms;
    }

    private void modifyCancellationPolicyForFlexiCancel(PersistedMultiRoomData persistedData, BookedRatePlan bookedRatePlan) {
        if (persistedData.getFlexiCancelAddOnInfo() != null && persistedData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails() != null &&
                persistedData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().isFlexiCancelApplied()) {
            if (bookedRatePlan.getCancellationPolicy() != null) {
                bookedRatePlan.getCancellationPolicy().setIconUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getFcInclusionIconUrl() : bookedRatePlan.getCancellationPolicy().getIconUrl());

                if (MapUtils.isNotEmpty(persistedData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getFlexiCancelAddOnState()) &&
                        isFlexiCancelSelectedOnPayment(persistedData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getFlexiCancelAddOnState().get(FLEXI_CANCEL))) {
                    bookedRatePlan.getCancellationPolicy().setText(persistedData.getFlexiCancelAddOnInfo().getFlexiCancelBasicDetails().getCancellationPolicyText() + polyglotService.getTranslatedData(FLEXI_CANCEL_THANKYOU_CANCELLATION_POLICY_TEXT));
                    bookedRatePlan.getCancellationPolicy().setType(BookedCancellationPolicyType.FC);
                    bookedRatePlan.getCancellationPolicy().setSubText(null);
                    bookedRatePlan.getCancellationPolicy().setDescription(null);
                } else {
                    bookedRatePlan.getCancellationPolicy().setText(bookedRatePlan.getCancellationPolicy().getText() + polyglotService.getTranslatedData(FLEXI_CANCEL_THANKYOU_CANCELLATION_POLICY_TEXT));
                }
            }
        }
    }

    private boolean isFlexiCancelSelectedOnPayment(AddOnState addOnState) {
        return addOnState != null && addOnState.name().equalsIgnoreCase(AddOnState.PAYMENT.name());
    }

    private void setSellableTypeAndEntirePropertyText(PersistedMultiRoomData persistedData, ThankYouResponse thankYouResponse, Map<String, Integer> roomBedCountMap, int totalRooms, Map<String, String> expDataMap) {
        Tuple<String, String> roomBedTuple = utility.getGuestRoomKeyValue(roomBedCountMap,
                persistedData.getHotelList().get(0).getHotelInfo().getPropertyType(),
                persistedData.getHotelList().get(0).getHotelInfo().getCountryCode(), false, persistedData.getHotelList().get(0).getHotelInfo().isAltAcco(), persistedData.getHotelList().get(0).getHotelInfo().getBedInfoText(),
                persistedData.getHotelList().get(0).getHotelInfo().getListingType(), persistedData.isServiceApartment(),0, expDataMap, false);
        thankYouResponse.getHotelDetails().setGuestRoomKey(roomBedTuple.getX());
        thankYouResponse.getHotelDetails().setGuestRoomValue(roomBedTuple.getY());
        thankYouResponse.getHotelDetails().setEntirePropertyText(roomBedTuple.getY());
        thankYouResponse.getHotelDetails().setEntireProperty(LISTING_TYPE_ENTIRE.equalsIgnoreCase(persistedData.getHotelList().get(0).getHotelInfo().getListingType()));
        if (totalRooms > 1) {
            if(roomBedCountMap.get(SELLABLE_BED_TYPE) == 0)
                thankYouResponse.getHotelDetails().setSellableType("Rooms");
            else
                thankYouResponse.getHotelDetails().setSellableType("Beds");
        }
        else{
            if(roomBedCountMap.get(SELLABLE_BED_TYPE) == 0)
                thankYouResponse.getHotelDetails().setSellableType("Room");
            else
                thankYouResponse.getHotelDetails().setSellableType("Bed");
        }
    }

    protected String getMyTripsDeepLink(String myTripsDeeplink, BookingMetaInfo bookingMetaInfo) {
        HashMap<String, String> params = new HashMap<>();
        if (null != bookingMetaInfo && StringUtils.isNotBlank(bookingMetaInfo.getBookingId())) {
            params.put(BOOKING_ID, bookingMetaInfo.getBookingId());
        }
        if (bookingMetaInfo != null) {
            boolean isCorp = CORP_ID_CONTEXT.equalsIgnoreCase(bookingMetaInfo.getRequestorIdContext());
            params.put(IS_CORPORATE, String.valueOf(isCorp));
        }
        return Utility.appendQueryParamsInUrl(myTripsDeeplink, params);
    }

    private List<BookedInclusion> buildInclusions(PersistedTariffInfo persistedTariffInfo, HotelInfo hotelInfo,
                                                  Map<String, String> expDataMap, boolean isExtraAdultChildInclusion) {
        List<MealPlan> bookedMealPlan = persistedTariffInfo.getMealPlans();
        List<Inclusion> bookedInclusionList = persistedTariffInfo.getInclusions();
        List<Integer> childAges = null;
        if (null != persistedTariffInfo.getAvailDetails()
                && null != persistedTariffInfo.getAvailDetails().getOccupancyDetails()) {
            childAges = persistedTariffInfo.getAvailDetails().getOccupancyDetails().getChildAges();
        }
        return utility.transformInclusions(
                bookedMealPlan, bookedInclusionList, mealPlanMapPolyglot,
                persistedTariffInfo.getSupplierDetails() != null ? persistedTariffInfo.getSupplierDetails().getSupplierCode() : "",
                null, null, expDataMap, hotelInfo.isAltAcco(), null,
                hotelInfo.getChainCode(), hotelInfo.getHtlAttributes(), childAges, hotelInfo.getCountryCode(), false, null, false, isExtraAdultChildInclusion);
    }

    // HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
    private BookedCancellationPolicy buildCancellationPolicy(PersistedTariffInfo persistedTariffInfo, boolean isBnplNewVariant, BNPLVariant bnplVariant, boolean expThankYouRevamp) {
        String partialRefundText = utility.buildPartialRefundDateText(persistedTariffInfo.getCancellationTimeline());
        return utility.transformCancellationPolicy(persistedTariffInfo.getCancelPenaltyList(), false, isBnplNewVariant, bnplVariant, null, polyglotService.getTranslatedData(CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null, Optional.empty(), partialRefundText, expThankYouRevamp);
    }

    private List<Traveller> buildTravellersDetails(PersistedMultiRoomData persistedData) {
        List<Traveller> travellers = persistedData.getTravelerInfoList().stream()
                .map(travelerInfo -> {
                    Traveller traveller = new Traveller();
                    traveller.setFirstName(travelerInfo.getFirstName());
                    traveller.setLastName(travelerInfo.getLastName());
                    traveller.setTitle(travelerInfo.getTitle());
                    return traveller;
                }).collect(Collectors.toList());
        return travellers;
    }

    private void buildGSTDetails(PersistedMultiRoomData persistedMultiRoomData, ThankYouResponse thankYouResponse){
        TravelerInfo masterTraveler = persistedMultiRoomData.getTravelerInfoList()
                .stream()
                .filter(travelerInfo -> travelerInfo.isMasterPax())
                .findFirst()
                .orElse(null);
        if(masterTraveler != null && StringUtils.isNotBlank(masterTraveler.getRegisteredGstinNum())){
            thankYouResponse.setGstnExist(true);
        }

    }

    /**
     * Build booking details from persisted room data for thank you page.
     *
     * @param persistedData     persisted txn data.
     * @param totalAmount       total amount paid by the user for the current booking. This is required to build endingBookingCardInfo.
     * @param expThankYouRevamp
     * @return {@link BookingDetails} object.
     */
    private BookingDetails buildBookingDetails(PersistedMultiRoomData persistedData, AmountDetail totalAmount, boolean expThankYouRevamp) {
        BookingDetails bookingDetails = new BookingDetails();
        bookingDetails.setBookingId(persistedData.getBookingMetaInfo().getBookingId());
        if(!expThankYouRevamp){
            bookingDetails.setInsuranceInfo(buildInsuranceInfo(persistedData));
        }
        if (!isBlockPnr(persistedData)) {
            bookingDetails.setPnr(getPNR(persistedData));
        }
        bookingDetails.setBookerInfo(buildBookerInfo(persistedData));
        bookingDetails.setCheckInDate(persistedData.getAvailReqBody().getCheckin());
        if(checkIfSameDayBooking(persistedData)){
            bookingDetails.setCheckOutDate(persistedData.getAvailReqBody().getCheckin());
            bookingDetails.setCheckInTime("Day Use");
            bookingDetails.setCheckOutTime("Day Use");
        }else {
            bookingDetails.setCheckOutDate(persistedData.getAvailReqBody().getCheckout());
            if (CollectionUtils.isNotEmpty(persistedData.getHotelList()) && persistedData.getHotelList().get(0).getHotelInfo() != null) {
                bookingDetails.setCheckInTime(StringUtils.isNotEmpty(persistedData.getHotelList().get(0).getHotelInfo().getCheckInTimeRange())?persistedData.getHotelList().get(0).getHotelInfo().getCheckInTimeRange():persistedData.getHotelList().get(0).getHotelInfo().getCheckInTime());
                bookingDetails.setCheckOutTime(StringUtils.isNotEmpty(persistedData.getHotelList().get(0).getHotelInfo().getCheckOutTimeRange())?persistedData.getHotelList().get(0).getHotelInfo().getCheckOutTimeRange():persistedData.getHotelList().get(0).getHotelInfo().getCheckOutTime());
                bookingDetails.setShowTimeRangeUi(persistedData.getHotelList().get(0).getHotelInfo().isShowTimeRangeUi());
            }
        }
        if(!expThankYouRevamp){
            bookingDetails.setCheckInPolicyDesc(getCheckInPolicyDescription(persistedData));
        }
        bookingDetails.setPaymentMode(persistedData.getAvailReqBody().getPayMode());
        bookingDetails.setBlackRegSuccess(persistedData.isBlackRegSuccess());
        bookingDetails.setRequestToBook(persistedData.isRequestToBook());
        bookingDetails.setRtbPreApproved(persistedData.isRtbPreApproved());
        bookingDetails.setRtbAutoCharge(persistedData.isRtbAutoCharge());

        if(persistedData.getTimeOfBooking()!=null)
            bookingDetails.setBookingDate(persistedData.getTimeOfBooking());

        List<PersistedHotel> hotelList = persistedData.getHotelList();
        boolean negotiatedRateFlag = CollectionUtils.isNotEmpty(hotelList) && hotelList.get(0).getHotelInfo() != null && RTB_EMAIL.equalsIgnoreCase(hotelList.get(0).getHotelInfo().getRpBookingModel());

        if (StringUtils.isNotEmpty(bookingDetails.getPnr()) && YET_TO_BE_GENERATED.equalsIgnoreCase(bookingDetails.getPnr())) {
            bookingDetails.setStatus(BookingStatus.PENDING);
        } else {
            bookingDetails.setStatus(buildBookingStatus(persistedData, negotiatedRateFlag));
        }
        bookingDetails.setDonated(isDonationPresentInBookig(persistedData));
        bookingDetails.setNights(getBookingNights(bookingDetails.getCheckInDate(), bookingDetails.getCheckOutDate()));
        bookingDetails.setDoubleBlackValidated(persistedData.isDoubleBlackValidated());
        if (BookingStatus.PENDING.equals(bookingDetails.getStatus())) {
            bookingDetails.setPendingBookingCardInfo(buildPendingBookingCardInfo(persistedData, negotiatedRateFlag, totalAmount));
        } else if (BookingStatus.FAILED.equals(bookingDetails.getStatus())) {
            bookingDetails.setFailedBookingCardInfo(buildFailedBookingCardInfo(persistedData));
        }
        return bookingDetails;
    }

    private boolean checkIfSameDayBooking(PersistedMultiRoomData persistedData){

        boolean sameDayUseBooking = false;
        try {
            for (PersistedTariffInfo tarrifInfo : persistedData.getHotelList().get(0).getTariffInfoList()) {
                if (StringUtils.isNotBlank(tarrifInfo.getRoomTypeName())) {
                    sameDayUseBooking = sameDayRoomNames.stream()
                            .anyMatch(e -> tarrifInfo.getRoomTypeName().toLowerCase().contains(e.toLowerCase()));
                    if (sameDayUseBooking) {
                        break;
                    }
                }
            }
        }catch(Exception e){
            logger.error("Error while checking for same day booking",e);
        }
        return sameDayUseBooking;
    }

    // [HTL-41643] For IH hotels, we send pnr value as null for some vendors to avoid frauds.
    private boolean isBlockPnr(PersistedMultiRoomData persistedData) {
        if (null != persistedData && null != persistedData.getAvailReqBody() && persistedData.getAvailReqBody().isBlockPnr()) {
            logger.debug("PNR status is blocked for the supplier {} ", persistedData.getAvailReqBody().getSupplierCode());
            return true;
        }

        return false;
    }

    private Integer getBookingNights(String checkin, String checkout) {
        return Math.toIntExact(Math.abs(ChronoUnit.DAYS.between(LocalDate.parse(checkin), LocalDate.parse(checkout))));
    }

    private boolean isDonationPresentInBookig(PersistedMultiRoomData persistedData) {
        boolean donated = false;
        if (persistedData.getAddOnInfo() != null
                && CollectionUtils.isNotEmpty(persistedData.getAddOnInfo().getAddOnNode())) {
            Optional<AddOnNode> donatedAddon = persistedData.getAddOnInfo().getAddOnNode().stream()
                    .filter(addOnNode -> LOB_DONATION.equalsIgnoreCase(addOnNode.getLob()))
                    .findFirst();
            if (donatedAddon.isPresent())
                donated = true;
        }
        return donated;
    }

    private FailedBookingCardInfo buildFailedBookingCardInfo(PersistedMultiRoomData persistedData) {
        FailedBookingCardInfo failedBookingCardInfo = new FailedBookingCardInfo();
        failedBookingCardInfo.setCardNum(null != persistedData.getPaymentInfo() ? persistedData.getPaymentInfo().getCardNumber() : null);
        failedBookingCardInfo.setDetailPageDeepLink(getDetailPageDeepLink(persistedData));
        failedBookingCardInfo.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedData.getUserGlobalInfo(), persistedData.getExpData()), persistedData.getBookingMetaInfo()));
        failedBookingCardInfo.setDuration(polyglotService.getTranslatedData(FAILED_BOOKING_REFUND_DURATION));
        return failedBookingCardInfo;
    }

    private String getDetailPageDeepLink(PersistedMultiRoomData persistedData) {
        String detailPageDeepLink = null;
        if (null == persistedData || null == persistedData.getAvailReqBody()
                || CollectionUtils.isEmpty(persistedData.getAvailReqBody().getHotelIds()))
            return detailPageDeepLink;
        String hotelId = persistedData.getAvailReqBody().getHotelIds().get(0);
        String checkInDate = dateUtil.getDateFormatted(persistedData.getAvailReqBody().getCheckin(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY);
        String checkOutDate = dateUtil.getDateFormatted(persistedData.getAvailReqBody().getCheckout(), DateUtil.YYYY_MM_DD, DateUtil.MMDDYYYY);
        String countryCode = persistedData.getAvailReqBody().getCountryCode();
        String cityCode = StringUtils.isNotBlank(persistedData.getAvailReqBody().getLocationId()) ? persistedData.getAvailReqBody().getLocationId() : persistedData.getAvailReqBody().getCityCode();
        List<RoomStayCandidate> roomStayCandidates = new ArrayList<>();
        for (RoomCriterion roomCriterion : persistedData.getAvailReqBody().getRoomCriteria()) {
            if (roomCriterion != null && CollectionUtils.isNotEmpty(roomCriterion.getRoomStayCandidates()))
                roomStayCandidates.addAll(roomCriterion.getRoomStayCandidates());
        }
        String roomStayQualifier = Utility.buildRoomStayQualifierFromRoomStayCandidates(roomStayCandidates, tildeRequiredInRSQ());
        String currency = persistedData.getAvailReqBody().getCurrency();

        detailPageDeepLink = MessageFormat.format(getHotelDetailsRawDeepLinkUrl(persistedData), hotelId, checkInDate, checkOutDate, countryCode, cityCode, roomStayQualifier, currency);
        return detailPageDeepLink;
    }

    private PendingBookingCardInfo buildPendingBookingCardInfo(PersistedMultiRoomData persistedData, boolean negotiatedRateFlag, AmountDetail totalAmount) {
        PendingBookingCardInfo pendingBookingCardInfo = new PendingBookingCardInfo();

        // Check if booking is negotiated rate booking.
        if (negotiatedRateFlag) {
            pendingBookingCardInfo.setPendingTime(polyglotService.getTranslatedData(PENDING_BOOKING_CONFIRMATION_TIME_NEGOTIATED_RATE));
            pendingBookingCardInfo.setTitle(polyglotService.getTranslatedData(PENDING_CARD_INFO_TITLE));

            String subtitle = StringUtils.EMPTY;
            if (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
                subtitle = polyglotService.getTranslatedData(PENDING_CARD_INFO_SUBTITLE_MOBILE);
            } else {
                subtitle = polyglotService.getTranslatedData(PENDING_CARD_INFO_SUBTITLE);
            }
            // Get currency symbol based on user selected currency.
            String currencySymbol = Currency.getCurrencyEnum(Utility.getAskedCurrency(persistedData)).getCurrencySymbol();
            subtitle = StringUtils.replace(subtitle, "{CURRENCY_SYMBOL}", OPEN_BOLD_TAG + currencySymbol + CLOSE_BOLD_TAG);
            subtitle = StringUtils.replace(subtitle, "{TOTAL_AMOUNT}", OPEN_BOLD_TAG + String.valueOf(totalAmount != null && totalAmount.getAmount() != null ? totalAmount.getAmount().intValue() : null)+ CLOSE_BOLD_TAG);
            pendingBookingCardInfo.setSubtitle(subtitle);

        } else {
            pendingBookingCardInfo.setPendingTime(polyglotService.getTranslatedData(PENDING_BOOKING_CONFIRMATION_TIME));
        }
        if (persistedData.isRequestToBook() && persistedData.isBnplZeroVariantBooking() && persistedData.getTotalDisplayFare() != null &&
                persistedData.getTotalDisplayFare().getBnplVariant() != null && BNPLVariant.BNPL_AT_0.name().equalsIgnoreCase(persistedData.getTotalDisplayFare().getBnplVariant().name())) {
            String date = persistedData.getCancellationTimeline() != null && StringUtils.isNotEmpty(persistedData.getCancellationTimeline().getTillDate()) ? persistedData.getCancellationTimeline().getTillDate() : null;
            if (StringUtils.isNotEmpty(date)) {
                Optional<ThankYouAmountLabel> remainingAmount = persistedData.getAmountLabels().stream().filter(amount -> amount != null && AMOUNT_LABEL_REMAINING_AMOUNT.equalsIgnoreCase(amount.getLabelType())).findFirst();
                if (remainingAmount.isPresent()) {
                    String subTitle = polyglotService.getTranslatedData(RTB_BNPL_0_THANK_YOU_TEXT);
                    pendingBookingCardInfo.setSubtitle(subTitle.replace("{amount}", OPEN_BOLD_TAG + String.valueOf(remainingAmount.get().getAmount()) + CLOSE_BOLD_TAG).replace("{date}", OPEN_BOLD_TAG + date + CLOSE_BOLD_TAG));
                }
            }
        }
        pendingBookingCardInfo.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedData.getUserGlobalInfo(), persistedData.getExpData()), persistedData.getBookingMetaInfo()));
        pendingBookingCardInfo.setCtaText(polyglotService.getTranslatedData(PENDING_CTATEXT));
        return pendingBookingCardInfo;
    }

    private BookerInfo buildBookerInfo(PersistedMultiRoomData persistedData) {
        BookerInfo bookerInfo = new BookerInfo();
        bookerInfo.setEmailId(persistedData.getTravelerInfoList().get(0).getEmail());
        String isdCode = "";
        if (StringUtils.isNotBlank(persistedData.getTravelerInfoList().get(0).getIsdCode())) {
            if (!persistedData.getTravelerInfoList().get(0).getIsdCode().startsWith("+")) {
                isdCode += "+";
            }
            isdCode += persistedData.getTravelerInfoList().get(0).getIsdCode() + "-";
        }
        bookerInfo.setMobileNum(isdCode + persistedData.getTravelerInfoList().get(0).getMobileNumber());
        return bookerInfo;
    }

    private String getCheckInPolicyDescription(PersistedMultiRoomData persistedData) {
        RatePolicy checkInPolicy = getCheckinPolicy(persistedData);
        if (checkInPolicy != null && StringUtils.isNotBlank(checkInPolicy.getShortDescription())) {
            if (".".equalsIgnoreCase(String.valueOf(checkInPolicy.getShortDescription().charAt(checkInPolicy.getShortDescription().length() - 1)))) {
                return checkInPolicy.getShortDescription().substring(0, checkInPolicy.getShortDescription().length() - 1);
            }
            return checkInPolicy.getShortDescription();
        }
        return null;
    }

    private BookingStatus buildBookingStatus(PersistedMultiRoomData persistedData, boolean negotiatedRateFlag) {
        if ((null!=persistedData.getBookingResponse() && BOOKING_STATUS_SUCCESS.equalsIgnoreCase(persistedData.getBookingResponse().getStatus()))) {
            if (BOOKING_NON_INSTANT_CONFIRMATION.equalsIgnoreCase(persistedData.getBookingResponse().getInstantConfirmation())
            || (persistedData.isRequestToBook() && !persistedData.isRtbPreApproved()) || negotiatedRateFlag) {
                return BookingStatus.PENDING;
            }
            return BookingStatus.SUCCESS;
        }
        return BookingStatus.FAILED;
    }

    private String getPNR(PersistedMultiRoomData persistedData) {
        String pnr = null;

        if (persistedData!= null && persistedData.getBookingResponse() != null &&
                CollectionUtils.isNotEmpty(persistedData.getBookingResponse().getHotelReservationIds())) {
            Optional<HotelReservationId> pnrReservation = persistedData.getBookingResponse().getHotelReservationIds().stream()
                    .filter(hotelReservationId -> BOOKING_PNR_KEY.equalsIgnoreCase(hotelReservationId.getType()))
                    .findFirst();
            if (pnrReservation.isPresent() && StringUtils.isNotBlank(pnrReservation.get().getValue()))
                pnr = pnrReservation.get().getValue();
        }
        return pnr;
    }

    private AmountDetail buildTotalAmount(PersistedMultiRoomData persistedData) {
        AmountDetail totalAmount = null;
        if(persistedData.getAmountLabels() == null) return null;
        Optional<ThankYouAmountLabel> totalPaidBooking = persistedData.getAmountLabels().stream().filter(thankYouAmountLabel ->
                AMOUNT_LABEL_TOTAL_AMOUNT.equalsIgnoreCase(thankYouAmountLabel.getLabelType())).findFirst();
        if (totalPaidBooking.isPresent()) {
            String currency;
            double amount;

            if (persistedData.getPaymentInfo() != null) {
                currency = persistedData.getPaymentInfo().getChargedCurrency();
                amount = Utility.round(persistedData.getPaymentInfo().getAmountCharged(),0);
            } else  {
                currency = totalPaidBooking.get().getAmountText().split("\\s+")[1];
                amount = Utility.round(Math.round(totalPaidBooking.get().getAmount()), 0);
            }

            totalAmount = new AmountDetail();
            totalAmount.setTitle(polyglotService.getTranslatedData(AMOUNT_BREAKUP_TOTAL));
            totalAmount.setAmount(amount);
            totalAmount.setCurrency(currency);
        }
        return totalAmount;
    }

    private List<AmountDetail> buildAmountBreakup(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail amountPaid = new AmountDetail();
        AmountDetail amountPending = new AmountDetail();
        AmountDetail amountTotal = new AmountDetail();
        double tcsAmount = persistedMultiRoomData.getTotalDisplayFare() != null && persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown() != null ?
                persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown().getTcsAmount() : 0.0;
        double paidAmount = 0, pendingAmount = 0, walletAmount = 0.0;
        boolean isPah = false, isBNPL = false;
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getAmountLabels()))
            return null;
        for (ThankYouAmountLabel thankYouAmountLabel : persistedMultiRoomData.getAmountLabels()) {
            switch (thankYouAmountLabel.getLabelType()) {
                case AMOUNT_LABEL_MMT_WALLET:
                    walletAmount = thankYouAmountLabel.getAmount();
                    break;
                case AMOUNT_LABEL_OTHER_PAYMODES:
                case AMOUNT_LABEL_AMOUNT_CARD:
                    paidAmount += thankYouAmountLabel.getAmount();
                    break;
                case AMOUNT_LABEL_REMAINING_AMOUNT:
                    isBNPL = true;
                    pendingAmount += thankYouAmountLabel.getAmount() + persistedMultiRoomData.getBnplExtraFees() + tcsAmount;
                    break;
                case AMOUNT_LABEL_AMOUNT_HOTEL:
                    isPah = true;
                    pendingAmount += thankYouAmountLabel.getAmount();
                    break;
                default:
                    break;
            }
        }

        boolean isMpFareHoldEligible = persistedMultiRoomData.isBookNowFareHold() && persistedMultiRoomData.getMpaFareHoldStatus()!=null
        && persistedMultiRoomData.getMpaFareHoldStatus().isHoldEligible() && persistedMultiRoomData.getMpaFareHoldStatus().isEligibleForHoldBooking();

        boolean isMpFareHoldZeroEligible = isMpFareHoldEligible ? (persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount()==0) : false;

//        if (isMpFareHoldEligible && (persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount()==0f)) {
//            logger.debug("MPFAREHOLD Zero Flow on thank-you page for amount Labels {}",persistedMultiRoomData.getAmountLabels());
//            paidAmount = 0;
//            ThankYouAmountLabel totalAmountLabel = persistedMultiRoomData.getAmountLabels().stream().filter(amountDetail -> AMOUNT_LABEL_TOTAL_AMOUNT.equalsIgnoreCase(amountDetail.getLabelType()))
//                    .findFirst().orElse(null);
//            //pending amount will be equal to total amount as this no payment has done in this flow.
//            pendingAmount = totalAmountLabel.getAmount();
//        }

        double pgCharges = 0;
        if (persistedMultiRoomData.getPaymentInfo() != null) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
            pgCharges += persistedMultiRoomData.getPaymentInfo().getPgCharges();
            pgCharges /= conversionFactor;

        }
        /*pgCharges not to be included as from payment team it is already included*/
        boolean isMyPartnerRequest = Objects.nonNull(persistedMultiRoomData.getAvailReqBody()) && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType());
        if (!isMyPartnerRequest) {
            paidAmount += pgCharges;
        }
        String currency = null != persistedMultiRoomData.getBookingMetaInfo() ? persistedMultiRoomData.getBookingMetaInfo().getCurrencyCode() : "INR";
        String supplierCurrency = Utility.getHotelierCurrency(persistedMultiRoomData);
        String askedCurrency = Utility.getAskedCurrency(persistedMultiRoomData);

        if(persistedMultiRoomData.isMyPartnerMoveToTdsTaxStructure() && isMpFareHoldEligible) {
            if (!isMpFareHoldZeroEligible){
                paidAmount -= walletAmount;
            }
            else if (walletAmount > 0) {
                paidAmount += walletAmount;
            }
        }
        amountPaid.setTitle(polyglotService.getTranslatedData(AMOUNT_BREAKUP_DEDUCTED));
        amountPaid.setAmount(Utility.round(paidAmount, 0));
        amountPaid.setCurrency(currency);
        if (pgCharges > 0.0) {
            String currSign = currency.equalsIgnoreCase("INR") ? "Rs" : currency;
            Integer roundedPgCharges = (int) Math.round(pgCharges);
            amountPaid.setSubtitle(polyglotService.getTranslatedData(PG_CHARGES_TEXT).replace("{currency}", currSign).replace("{amount}", roundedPgCharges.toString()));
        }

        amountPending.setTitle(polyglotService.getTranslatedData(AMOUNT_BREAKUP_PENDING));
        amountPending.setAmount(Utility.round(pendingAmount, 0));
        amountPending.setCurrency(currency);
        // check if its bnpl and there is upi mandate
        if (isBNPL && isAutoPay(persistedMultiRoomData)) {
            if (persistedMultiRoomData.isBnplNewVariant()) {
                String translatedData = polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_MANDATE_TEXT);
                if (StringUtils.isNotBlank(translatedData)) {
                    amountPending.setSubtitle(MessageFormat.format(translatedData, getFormattedMandateDate(persistedMultiRoomData.getPaymentInfo().getMandateDetails().getMandateDate())));
                }
            }
        } else if (isBNPL && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())
                && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList())
                && persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline() != null) {
            if (persistedMultiRoomData.isBnplNewVariant() || persistedMultiRoomData.isBnplZeroVariantBooking()) {
                String translatedData = polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT);
                if (StringUtils.isNotBlank(translatedData)) {
                    String cardChargeDateLong = StringUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong()) ?
                            persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong() :
                            persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline() != null ?
                                    persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline().getCardChargeDateLong() : null;
                    String cardChargeDateTime = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeTime();
                    if (StringUtils.isNotBlank(cardChargeDateTime)) {
                        cardChargeDateLong = cardChargeDateLong + ", " + cardChargeDateTime;
                    }
                    amountPending.setSubtitle(MessageFormat.format(translatedData, cardChargeDateLong));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_TEXT),
                        StringUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong())
                                ? persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong() :
                                persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline() != null ?
                                        persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline().getCardChargeDateLong() : null));
            }
        } else if (isBNPL && persistedMultiRoomData.getCancellationTimeline() != null
                && StringUtils.isNotBlank(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong())) {
            if (persistedMultiRoomData.isBnplNewVariant() || persistedMultiRoomData.isBnplZeroVariantBooking()) {
                String translatedData = polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT);
                if (StringUtils.isNotBlank(translatedData)) {
                    String cardChargeDateLong = StringUtils.isNotEmpty(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()) ? persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()
                            : persistedMultiRoomData.getOriginalCancellationTimeline() != null ? persistedMultiRoomData.getOriginalCancellationTimeline().getCardChargeDateLong() : null;
                    String cardChargeDateTime = persistedMultiRoomData.getCancellationTimeline().getCardChargeTime();
                    if (StringUtils.isNotBlank(cardChargeDateTime)) {
                        cardChargeDateLong = cardChargeDateLong + ", " + cardChargeDateTime;
                    }
                    amountPending.setSubtitle(MessageFormat.format(translatedData, cardChargeDateLong));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_TEXT),
                        StringUtils.isNotEmpty(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()) ? persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong() :
                                persistedMultiRoomData.getOriginalCancellationTimeline() != null ? persistedMultiRoomData.getOriginalCancellationTimeline().getCardChargeDateLong() : null));
            }
        }
        if(StringUtils.isNotEmpty(amountPending.getSubtitle())) {
            amountPending.setSubtitle(getSubTitleWithZone(amountPending.getSubtitle()));
        }
        amountTotal.setTitle(polyglotService.getTranslatedData(AMOUNT_BREAKUP_TOTAL));
        amountTotal.setAmount(Utility.round(paidAmount + pendingAmount, 0));
        amountTotal.setCurrency(currency);

        if (isPah) {
            amountPending.setSubtitle(polyglotService.getTranslatedData(PENDING_AMOUNT_PAH_DEFAULT_TEXT));
        }

        if ((isPah || isMpFareHoldZeroEligible) && !supplierCurrency.equals(askedCurrency)) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);

            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
            if (Utility.isRegionGccOrKsa(region)) {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.PENDING_AMOUNT_PAH_TEXT), polyglotService.getTranslatedData(ConstantsTranslation.LOCAL_CURRENCY_TEXT)));
            }
            else{
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_PAH_TEXT), supplierCurrency));
                amountPending.setAlternateCurrency(supplierCurrency);
                amountPending.setAlternateAmount(Utility.round(amountPending.getAmount(), 2));
                amountTotal.setAlternateCurrency(supplierCurrency);
                amountTotal.setAlternateAmount(Utility.round(amountTotal.getAmount(), 2));
            }
            amountPending.setAmount(Utility.round(pendingAmount / conversionFactor, 0));
            amountPending.setCurrency(askedCurrency);
            amountPaid.setAmount(Utility.round(paidAmount / conversionFactor, 0));
            amountPaid.setCurrency(askedCurrency);
            amountTotal.setAmount(Utility.round((paidAmount + pendingAmount) / conversionFactor, 0));
            amountTotal.setCurrency(askedCurrency);
        }

        AmountDetail persuasionAmountDetail = new AmountDetail();
        if (isBNPL) {
            if (thankYouResponse.getBookingDetails() != null)
                thankYouResponse.getBookingDetails().setBnplBooking(isBNPL);
            persuasionAmountDetail.setTitle(polyglotService.getTranslatedData(BNPL_PAYMENT_BREAKUP_LABEL));
        } else if (isPah) {
            if (thankYouResponse.getBookingDetails() != null)
                thankYouResponse.getBookingDetails().setPahBooking(isPah);
            persuasionAmountDetail.setTitle(polyglotService.getTranslatedData(PAH_PAYMENT_BREAKUP_LABEL));
        }
        List<AmountDetail> bookingAmountBreakup = new ArrayList<>(Arrays.asList(amountPaid, amountPending, amountTotal));
        if (null != persuasionAmountDetail && !isMpFareHoldEligible) {
            persuasionAmountDetail.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedMultiRoomData.getUserGlobalInfo(), persistedMultiRoomData.getExpData()), persistedMultiRoomData.getBookingMetaInfo()));
            bookingAmountBreakup.add(persuasionAmountDetail);
        }
        return bookingAmountBreakup;
    }

    private List<AmountDetail> buildAmountBreakupV2(PersistedMultiRoomData persistedMultiRoomData) {
        AmountDetail amountPaid = new AmountDetail();
        amountPaid.setKey("PAID_AMOUNT");
        AmountDetail amountPending = new AmountDetail();
        amountPending.setKey("PENDING_AMOUNT");

        double tcsAmount = persistedMultiRoomData.getTotalDisplayFare() != null && persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown() != null ?
                persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown().getTcsAmount() : 0.0;
        double paidAmount = 0, pendingAmount = 0, walletAmount = 0.0;
        boolean isPah = false, isBNPL = false;
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getAmountLabels()))
            return null;
        for (ThankYouAmountLabel thankYouAmountLabel : persistedMultiRoomData.getAmountLabels()) {
            switch (thankYouAmountLabel.getLabelType()) {
                case AMOUNT_LABEL_MMT_WALLET:
                    walletAmount = thankYouAmountLabel.getAmount();
                    break;
                case AMOUNT_LABEL_OTHER_PAYMODES:
                case AMOUNT_LABEL_AMOUNT_CARD:
                    paidAmount += thankYouAmountLabel.getAmount();
                    break;
                case AMOUNT_LABEL_REMAINING_AMOUNT:
                    isBNPL = true;
                    pendingAmount += thankYouAmountLabel.getAmount() + persistedMultiRoomData.getBnplExtraFees() + tcsAmount;
                    break;
                case AMOUNT_LABEL_AMOUNT_HOTEL:
                    isPah = true;
                    pendingAmount += thankYouAmountLabel.getAmount();
                    break;
                default:
                    break;
            }
        }

        boolean isMpFareHoldEligible = persistedMultiRoomData.isBookNowFareHold() && persistedMultiRoomData.getMpaFareHoldStatus()!=null
                && persistedMultiRoomData.getMpaFareHoldStatus().isHoldEligible() && persistedMultiRoomData.getMpaFareHoldStatus().isEligibleForHoldBooking();

        boolean isMpFareHoldZeroEligible = isMpFareHoldEligible ? (persistedMultiRoomData.getMpaFareHoldStatus().getBookingAmount()==0) : false;

        double pgCharges = 0;
        if (persistedMultiRoomData.getPaymentInfo() != null) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
            pgCharges += persistedMultiRoomData.getPaymentInfo().getPgCharges();
            pgCharges /= conversionFactor;
        }
        /*pgCharges not to be included as from payment team it is already included*/
        boolean isMyPartnerRequest = Objects.nonNull(persistedMultiRoomData.getAvailReqBody()) && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType());
        if (!isMyPartnerRequest) {
            paidAmount += pgCharges;
        }
        String currency = null != persistedMultiRoomData.getBookingMetaInfo() ? persistedMultiRoomData.getBookingMetaInfo().getCurrencyCode() : "INR";
        String supplierCurrency = Utility.getHotelierCurrency(persistedMultiRoomData);
        String askedCurrency = Utility.getAskedCurrency(persistedMultiRoomData);

        if(persistedMultiRoomData.isMyPartnerMoveToTdsTaxStructure() && isMpFareHoldEligible) {
            if (!isMpFareHoldZeroEligible){
                paidAmount -= walletAmount;
            }
            else if (walletAmount > 0) {
                paidAmount += walletAmount;
            }
        }
        amountPaid.setAmount(Utility.round(paidAmount, 0));
        amountPaid.setCurrency(currency);
        if (pgCharges > 0.0) {
            String currSign = currency.equalsIgnoreCase("INR") ? "Rs" : currency;
            Integer roundedPgCharges = (int) Math.round(pgCharges);
            amountPaid.setSubtitle(polyglotService.getTranslatedData(PG_CHARGES_TEXT).replace("{currency}", currSign).replace("{amount}", roundedPgCharges.toString()));
        }

        amountPending.setAmount(Utility.round(pendingAmount, 0));
        amountPending.setCurrency(currency);
        // check if its bnpl and there is upi mandate
        if (isBNPL && isAutoPay(persistedMultiRoomData)) {
            if (persistedMultiRoomData.isBnplNewVariant()) {
                String translatedData = polyglotService.getTranslatedData(polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_MANDATE_TEXT_V2));
                if (StringUtils.isNotBlank(translatedData)) {
                    amountPending.setSubtitle(MessageFormat.format(translatedData, getFormattedMandateDate(persistedMultiRoomData.getPaymentInfo().getMandateDetails().getMandateDate())));
                }
            }
        } else if (isBNPL && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())
                && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList())
                && persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline() != null) {
            if (persistedMultiRoomData.isBnplNewVariant() || persistedMultiRoomData.isBnplZeroVariantBooking()) {
                String translatedData = polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT_V2);
                if (StringUtils.isNotBlank(translatedData)) {
                    String cardChargeDateLong = StringUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong()) ?
                            persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong() :
                            persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline() != null ?
                                    persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline().getCardChargeDateLong() : null;
                    String cardChargeDateTime = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeTime();
                    if (StringUtils.isNotBlank(cardChargeDateTime)) {
                        cardChargeDateLong = cardChargeDateLong + ", " + cardChargeDateTime;
                    }
                    amountPending.setSubtitle(MessageFormat.format(translatedData, cardChargeDateLong));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_TEXT_V2),
                        StringUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong())
                                ? persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getCancellationTimeline().getCardChargeDateLong() :
                                persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline() != null ?
                                        persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getOriginalCancellationTimeline().getCardChargeDateLong() : null));
            }
        } else if (isBNPL && persistedMultiRoomData.getCancellationTimeline() != null
                && StringUtils.isNotBlank(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong())) {
            if (persistedMultiRoomData.isBnplNewVariant() || persistedMultiRoomData.isBnplZeroVariantBooking()) {
                String translatedData = polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_NEW_VARIANT_TEXT_V2);
                if (StringUtils.isNotBlank(translatedData)) {
                    String cardChargeDateLong = StringUtils.isNotEmpty(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()) ? persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()
                            : persistedMultiRoomData.getOriginalCancellationTimeline() != null ? persistedMultiRoomData.getOriginalCancellationTimeline().getCardChargeDateLong() : null;
                    String cardChargeDateTime = persistedMultiRoomData.getCancellationTimeline().getCardChargeTime();
                    if (StringUtils.isNotBlank(cardChargeDateTime)) {
                        cardChargeDateLong = cardChargeDateLong + ", " + cardChargeDateTime;
                    }
                    amountPending.setSubtitle(MessageFormat.format(translatedData, cardChargeDateLong));
                }
            } else {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_BNPL_TEXT_V2),
                        StringUtils.isNotEmpty(persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong()) ? persistedMultiRoomData.getCancellationTimeline().getCardChargeDateLong() :
                                persistedMultiRoomData.getOriginalCancellationTimeline() != null ? persistedMultiRoomData.getOriginalCancellationTimeline().getCardChargeDateLong() : null));
            }
        }
        if(StringUtils.isNotEmpty(amountPending.getSubtitle())) {
            amountPending.setSubtitle(getSubTitleWithZone(amountPending.getSubtitle()));
        }

        if (isPah) {
            amountPending.setSubtitle(polyglotService.getTranslatedData(PENDING_AMOUNT_PAH_DEFAULT_TEXT_V2));
        }

        if ((isPah || isMpFareHoldZeroEligible) && !supplierCurrency.equals(askedCurrency)) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);

            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
            if (AE.equalsIgnoreCase(region)) {
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_PAH_TEXT_V2), polyglotService.getTranslatedData(LOCAL_CURRENCY_TEXT)));
            }
            else{
                amountPending.setSubtitle(MessageFormat.format(polyglotService.getTranslatedData(PENDING_AMOUNT_PAH_TEXT_V2), supplierCurrency));
                amountPending.setAlternateCurrency(supplierCurrency);
                amountPending.setAlternateAmount(Utility.round(amountPending.getAmount(), 2));
            }
            amountPending.setAmount(Utility.round(pendingAmount / conversionFactor, 0));
            amountPending.setCurrency(askedCurrency);
            amountPaid.setAmount(Utility.round(paidAmount / conversionFactor, 0));
            amountPaid.setCurrency(askedCurrency);
        }

        return new ArrayList<>(Arrays.asList(amountPaid, amountPending));
    }

    private String getSubTitleWithZone(String subTitleWithRawZone) {
        subTitleWithRawZone = subTitleWithRawZone.replace("<zone>", Utility.isGccOrKsa()?GULF_STANDARD_TIME:IST);
        return subTitleWithRawZone;
    }

    /**
     * In case of BNPL booking, an option is present where an auto-pay mandate could be
     * created. On mandate date, this amount will be automatically debited from the UPI account of user.
     * This function checks if the mandateDetails for UPI auto-pay is present in the transaction data
     *
     * @param persistedMultiRoomData of type {@link PersistedMultiRoomData}
     * @return of type {@link  Boolean}
     */
    private boolean isAutoPay(PersistedMultiRoomData persistedMultiRoomData) {
        return null != persistedMultiRoomData && null != persistedMultiRoomData.getPaymentInfo()
                && null != persistedMultiRoomData.getPaymentInfo().getMandateDetails()
                && persistedMultiRoomData.getPaymentInfo().getMandateDetails().getMandateType().equalsIgnoreCase(AUTO_PAY);
    }

    /**
     * Takes an input date string of type dd-MM-yyyy HH:mm a and convert into dd MMM,yyyy format
     *
     * @param mandateDate of type {@link String}
     * @return {@link String}
     */
    private String getFormattedMandateDate(String mandateDate) {
        return dateUtil.getDateFormatted(mandateDate, DateUtil.DD_MM_YYYY_HH_MM_A, DateUtil.DD_MMM_YYYY);
    }

    private HotelResult buildHotelDetails(PersistedMultiRoomData persistedMultiRoomData) {
        HotelResult hotelResult = new HotelResult();
        if (CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList())) {
                        HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
            hotelResult.setName(hotelInfo.getName());
            hotelResult.setHotelIcon(hotelInfo.getHotelIcon());
            hotelResult.setAddressLines(hotelInfo.getAddressLines());
            hotelResult.setStarRating(hotelInfo.getStarRating());
            hotelResult.setStarRatingType(hotelInfo.getStarRatingType());
            hotelResult.setPropertyType(hotelInfo.getPropertyType());
            hotelResult.setPropertyLabel(hotelInfo.getPropertyLabel());
            hotelResult.setAltAcco(hotelInfo.isAltAcco());
            hotelResult.setCountryCode(hotelInfo.getCountryCode());
            //BedInfoText, It is added to summarize the bed types and count for property Layout
            hotelResult.setBedInfoText(hotelInfo.getBedInfoText());
            hotelResult.setCategories(commonResponseTransformer.getHotelCategories(hotelInfo.getCategories(), hotelInfo.isAltAcco(), utility.isMyPartnerExclusiveDealAllowed(persistedMultiRoomData)));
            hotelResult.setHotelId(hotelInfo.getHotelId());
            hotelResult.setCityName(hotelInfo.getCityName());
            hotelResult.setLocationId(hotelInfo.getLocationId());
            hotelResult.setLocationType(hotelInfo.getLocusData() != null ? hotelInfo.getLocusData().getLocusType() : null);
            hotelResult.setCategoryDetails(persistedMultiRoomData.getCategoryDetails());
            boolean isMyPartnerRequest = Objects.nonNull(persistedMultiRoomData.getAvailReqBody()) && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType());
            if(isMyPartnerRequest && persistedMultiRoomData.isPropertyGSTAssured() && persistedMultiRoomData.isAgentGSTAssured() && travellerGSTDetailsExist(persistedMultiRoomData.getTravelerInfoList())){
                CategoryTag categoryTag = new CategoryTag();
                categoryTag.setIconUrl(iconUrlGSTAssured);
                categoryTag.setStyle(new Style());
                categoryTag.getStyle().setIconHeight(29);
                categoryTag.getStyle().setIconWidth(134);
                categoryTag.setTooltip(commonConfigConsul.getMmtMyPartnerTooltip());
                hotelResult.setCategoryTag(categoryTag);
            }
            String idContext = MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue());

            if (hotelInfo.isBudgetHotel() && StringUtils.isNotBlank(idContext) && !CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
                String siteDomain = persistedMultiRoomData.getAvailReqBody().getSiteDomain();
                hotelResult.setHotelTags(commonResponseTransformer.buildValueStaysHotelTag(VALUE_STAY_TAG_TITLE_THANK_YOU, hotelResult.getHotelTags()));
            }
            buildGroupBookingSpecificNodes(hotelResult, persistedMultiRoomData, hotelInfo);
            if(StringUtils.isNotEmpty(persistedMultiRoomData.getIngoAltaccoTracking())) {
                hotelResult.setSupplierType(persistedMultiRoomData.getIngoAltaccoTracking());
            }


            // Set specialFare based on persisted data.
            hotelResult.setSpecialFare(hotelInfo.isCorpSpecialFare());
            hotelResult.setHotelCategories(utility.concatenateWithSeparator(PIPE, hotelInfo.getCategories()));
        }
        if (persistedMultiRoomData.getAvailReqBody() != null)
            hotelResult.setFunnelSource(StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getFunnelSource()) ? persistedMultiRoomData.getAvailReqBody().getFunnelSource() : "HOTELS");

        if(persistedMultiRoomData.getAvailReqBody() != null && FUNNEL_DAYUSE.equalsIgnoreCase(persistedMultiRoomData.getAvailReqBody().getFunnelSource())){
            hotelResult.setDayUseDetails(new DayUseDetails());
            if(persistedMultiRoomData.getAvailReqBody().getSlot() != null)
                hotelResult.getDayUseDetails().setStayTime(utility.calculateTimeSlot_Meridiem(persistedMultiRoomData.getAvailReqBody().getSlot()));
            hotelResult.getDayUseDetails().setDayUse(true);
        }
        return hotelResult;
    }

    private boolean travellerGSTDetailsExist(List<TravelerInfo> travelerInfoList) {
        for(TravelerInfo trvlinfo: travelerInfoList) {
            if (StringUtils.isNotBlank(trvlinfo.getRegisteredGstinNum()) && StringUtils.isNotBlank(trvlinfo.getGstinCompanyName())){
                return true;
            }
        }
        return false;
    }


    private PropertyRules buildPropertyRules(PersistedMultiRoomData persistedMultiRoomData, ThankYouResponse thankYouResponse) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList())) {
            return null;
        }

        String supplierCode =StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getSupplierCode()) ? persistedMultiRoomData.getAvailReqBody().getSupplierCode(): StringUtils.EMPTY;
        if(StringUtils.isBlank(supplierCode) && CollectionUtils.isNotEmpty(persistedMultiRoomData.getAvailReqBody().getRoomCriteria()) )
            supplierCode = persistedMultiRoomData.getAvailReqBody().getRoomCriteria().get(0).getSupplierCode();

        if(StringUtils.isBlank(supplierCode) && CollectionUtils.isNotEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList())  )
            supplierCode = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails() != null ?
                    persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails().getSupplierCode() : StringUtils.EMPTY;

        HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
        RequestInputBO inputBo = new RequestInputBO.Builder()
                .buildCountryCode(hotelInfo.getCountryCode())
                .buildPropertyType(hotelInfo.getPropertyType())
                .buildHouseRules(hotelInfo.getHouseRules())
                .buildMustReadRules(hotelInfo.getMustReadRules())
                .buildPah(PaymentMode.isPAHOnlyPaymodes(PaymentMode.findPaymentModeFromString(persistedMultiRoomData.getAvailReqBody().getPayMode())))
                .buildPahWithCC(Utility.isPahWithCCPaymode(persistedMultiRoomData.getAvailReqBody().getPayMode()))
                .buildCancellationPolicyType(getBookedCancellationPolicyType(thankYouResponse).name())
                .buildCancellationDate(getCancellationPolicyTillDate(persistedMultiRoomData))
                .buildSupplierCode( supplierCode )
                .buildCheckinPolicy(getCheckinPolicy(persistedMultiRoomData))
                .buildConfirmationPolicy(getConfirmationPolicy(persistedMultiRoomData))
                .buildNotices(hotelInfo.getNotices())
                .build();

        PropertyRules importantInfoSection = commonResponseTransformer.getImportantInfoSection(inputBo);
        if (null != importantInfoSection)
            importantInfoSection.setMyTripsDeeplink(getMyTripsDeepLink(getMytripsRawDeepLinkUrl(persistedMultiRoomData.getUserGlobalInfo(), persistedMultiRoomData.getExpData()), persistedMultiRoomData.getBookingMetaInfo()));
        return importantInfoSection;
    }

    private RatePolicy getCheckinPolicy(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        RatePolicy checkInPolicy = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            RatePolicy ratePolicy = persistedTariffInfo.getCheckinPolicy();
            if (checkInPolicy == null && ratePolicy != null) {
                checkInPolicy = ratePolicy;
                if (MOST_RESTRICTED_POLICY.equalsIgnoreCase(checkInPolicy.getMostRestrictive())) {
                    return checkInPolicy;
                }
            } else if (checkInPolicy != null && MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                checkInPolicy = ratePolicy;
                return checkInPolicy;
            }
        }
        return checkInPolicy;
    }

    private String getCancellationPolicyTillDate(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        CancelPenalty mostRestrictedCancelPenalty = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            if (mostRestrictedCancelPenalty == null) {
                mostRestrictedCancelPenalty = persistedTariffInfo.getCancelPenaltyList().get(0);
                if (MOST_RESTRICTED_POLICY.equalsIgnoreCase(mostRestrictedCancelPenalty.getMostRestrictive())) {
                    break;
                }
            }
            Optional<CancelPenalty> cancelPenalty = persistedTariffInfo.getCancelPenaltyList().stream().filter(penalty -> MOST_RESTRICTED_POLICY.equalsIgnoreCase(penalty.getMostRestrictive())).findFirst();
            if (cancelPenalty.isPresent()) {
                mostRestrictedCancelPenalty = cancelPenalty.get();
                break;
            }
        }
        if (null != mostRestrictedCancelPenalty && StringUtils.isNotBlank(mostRestrictedCancelPenalty.getTillDate()))
            return mostRestrictedCancelPenalty.getTillDate();
        return null;
    }

    private RatePolicy getConfirmationPolicy(PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()))
            return null;
        RatePolicy confirmationPolicy = null;
        for (PersistedTariffInfo persistedTariffInfo : persistedMultiRoomData.getHotelList().get(0).getTariffInfoList()) {
            RatePolicy ratePolicy = persistedTariffInfo.getConfirmationPolicy();
            if (confirmationPolicy == null && ratePolicy != null) {
                confirmationPolicy = ratePolicy;
                if (MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                    return confirmationPolicy;
                }
            } else if (confirmationPolicy != null && MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                confirmationPolicy = ratePolicy;
                return confirmationPolicy;
            }
        }
        return confirmationPolicy;
    }

    private SelectedSpecialRequests buildSelectedSpecialRequests(PersistedMultiRoomData persistedData) {
        SelectedSpecialRequests selectedSpecialRequest = null;
        if (CollectionUtils.isEmpty(persistedData.getHotelList())
                || null == persistedData.getHotelList().get(0).getHotelInfo()
                || persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable() == null
                || CollectionUtils.isEmpty(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable().getCategories())
                || persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest() == null
                || CollectionUtils.isEmpty(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest().getCategories()))
            return null;

        selectedSpecialRequest = commonResponseTransformer.buildSelctedSpecialRequests(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequestAvailable(),
                persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest());
        return selectedSpecialRequest;
    }

    private SelectedSpecialRequestsV2 buildSelectedSpecialRequestsV2(PersistedMultiRoomData persistedData) {
        SelectedSpecialRequestsV2 selectedSpecialRequest = null;
        if (CollectionUtils.isEmpty(persistedData.getHotelList())
                || null == persistedData.getHotelList().get(0).getHotelInfo()
                || persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest() == null
                || CollectionUtils.isEmpty(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest().getCategories()))
            return null;

        selectedSpecialRequest = buildSelectedSpecialRequestsV2Info(persistedData.getHotelList().get(0).getHotelInfo().getSpecialRequest());
        return selectedSpecialRequest;
    }

    public SelectedSpecialRequestsV2 buildSelectedSpecialRequestsV2Info(SpecialRequest specialRequestAvailable) {
        SelectedSpecialRequestsV2 selectedSpecialRequest = new SelectedSpecialRequestsV2();
        selectedSpecialRequest.setRequests(new ArrayList<>());
        selectedSpecialRequest.setText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_TEXT));
        selectedSpecialRequest.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_SUBTEXT));
        for (SpecialRequestCategory selectedCategory : specialRequestAvailable.getCategories()) {
            SelectedSpecialRequestsV2Info splReqInfo = new SelectedSpecialRequestsV2Info();

            if (SPECIAL_REQ_V2_OTHER_REQUEST_TEMPLATE_ID.equalsIgnoreCase(selectedCategory.getTemplateId())) {
                if (selectedCategory.getValues() != null && selectedCategory.getValues().length > 0 && StringUtils.isNotBlank(selectedCategory.getValues()[0])) {
                    splReqInfo.setName(selectedCategory.getValues()[0]);
                }
            } else {
                splReqInfo.setName(selectedCategory.getName());
                if (selectedCategory.getValues() != null && selectedCategory.getValues().length > 0 && StringUtils.isNotBlank(selectedCategory.getValues()[0])) {
                    splReqInfo.setInfo(selectedCategory.getValues()[0]);
                }
            }

            if(StringUtils.isNotEmpty(selectedCategory.getUserComment())){
                splReqInfo.setAdditionalNote(selectedCategory.getUserComment());
            }
            selectedSpecialRequest.getRequests().add(splReqInfo);
        }
        return selectedSpecialRequest;
    }


    private MyTripsSection buildMyTripsSection(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedData) {
        boolean isZeroPaymentNowBooking = false;
        if (thankYouResponse.getBookingDetails() != null)
            isZeroPaymentNowBooking = thankYouResponse.getBookingDetails().isBnplBooking() || thankYouResponse.getBookingDetails().isPahBooking();
        BookedCancellationPolicyType cancellationPolicyType = getBookedCancellationPolicyType(thankYouResponse);

        //get the condition key
        String apDays = "X";
        if (dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(thankYouResponse.getBookingDetails().getCheckInDate())) == 0)
            apDays = String.valueOf(0);
        StringBuilder conditionKey = new StringBuilder();
        if (isZeroPaymentNowBooking)
            conditionKey.append("ZP").append(UNDERSCORE);
        conditionKey.append(cancellationPolicyType.toString()).append(UNDERSCORE)
                .append("AP").append(UNDERSCORE).append(apDays);

        logger.warn("MyTrips Section condition key " + conditionKey.toString());


        try {
            // convert myTripsCardTypeToCardDetails here
            Map<String, String> expDataMap = buildExperimentData(persistedData);
            boolean mytripUrlV2 = expDataMap != null && TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.MYTRIP_URL_V2));
            String jsonToString = gson.toJson(mytripUrlV2 ? myTripsCardTypeToCardDetailsV2 : myTripsCardTypeToCardDetails);
            Map<String, String> textReplacer = null;
            myTripsCardTypeToCardDetailsModified = gson.fromJson(jsonToString, new TypeToken<Map<String, MyTripCard>>() {
            }.getType());
            if (persistedData.getBookerInfo().getProfileType().equalsIgnoreCase(PROFILE_TYPE_CTA))
                textReplacer = myTripsCtaTextReplacer;
            polyglotHelper.translateMyTripsCards(myTripsCardTypeToCardDetailsModified, textReplacer);

        }catch (Exception e){
            logger.warn("Error in translating myTripsCardTypeToCardDetail");
        }


        List<String> cardTypeList = myTripsConditionsToCardsList.get(conditionKey.toString());
        if (CollectionUtils.isEmpty(cardTypeList))
            return null;
        MyTripsSection myTripsSection = new MyTripsSection();
        List<MyTripCard> cards = cardTypeList.stream()
                .map(cardType -> {
                    MyTripCard myTripCard = myTripsCardTypeToCardDetailsModified.get(cardType);
                    updateMyTripIconUrl(myTripCard, persistedData, cardType);
                    myTripCard.setDeepLink(Utility.appendQueryParamsInUrl(getMytripsRawDeepLinkUrl(persistedData.getUserGlobalInfo(), persistedData.getExpData()), Collections.singletonMap(BOOKING_ID, thankYouResponse.getBookingDetails().getBookingId())));
                    return myTripCard;
                })
                .collect(Collectors.toList());
        myTripsSection.setCards(cards);
        return myTripsSection;
    }

    protected void updateMyTripIconUrl(MyTripCard myTripCard, PersistedMultiRoomData persistedData, String cardType) {
        boolean isCorp = false;
        if (null != persistedData && null != persistedData.getAvailReqBody() && StringUtils.isNotBlank(persistedData.getAvailReqBody().getIdContext())
                && "CORP".equalsIgnoreCase(persistedData.getAvailReqBody().getIdContext()))
            isCorp = true;
        String iconUrl = "";
        if (isCorp)
            iconUrl = getMytripActionCorpUrl(cardType);
        else
            iconUrl = getMytripActionB2CUrl(cardType);
        myTripCard.setIconUrl(iconUrl);
    }

    private BookedCancellationPolicyType getBookedCancellationPolicyType(ThankYouResponse thankYouResponse) {
        BookedCancellationPolicyType cancellationPolicyType = BookedCancellationPolicyType.NR;
        if (CollectionUtils.isNotEmpty(thankYouResponse.getRooms().getRatePlanList())
                && thankYouResponse.getRooms().getRatePlanList().get(0).getCancellationPolicy() != null
                && thankYouResponse.getRooms().getRatePlanList().get(0).getCancellationPolicy().getType().equals(BookedCancellationPolicyType.FC)) {
            cancellationPolicyType = BookedCancellationPolicyType.FC;
        }
        return cancellationPolicyType;
    }

    private void buildAdditionalCharges(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if (CollectionUtils.isEmpty(persistedMultiRoomData.getHotelList()) || persistedMultiRoomData.getHotelList().get(0) == null || persistedMultiRoomData.getHotelList().get(0).getHotelInfo() == null)
            return;
        HotelInfo hotelInfo = persistedMultiRoomData.getHotelList().get(0).getHotelInfo();
        AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
                .buildUserCurrency(persistedMultiRoomData.getAvailReqBody().getCurrency())
                .buildHotelierCurrency(Utility.getHotelierCurrency(persistedMultiRoomData))
                .buildPropertyType(hotelInfo.getPropertyType())
                .buildAdditionalFees(hotelInfo.getMandatoryCharges())
                .buildConversionFactor(persistedMultiRoomData.getTotalDisplayFare().getConversionFactor())
                .buildBookingAmount(persistedMultiRoomData.getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice())
                .buildCityName(hotelInfo.getCityName())
                .buildCountryCode(hotelInfo.getCountryCode())
                .build();
        thankYouResponse.setAdditionalfees(commonResponseTransformer.buildAdditionalCharges(
                additionalChargesBO, false,hotelInfo.getListingType(),
                PAGE_CONTEXT_THANK_YOU, null));
    }

    private void buildPgCharges(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if(persistedMultiRoomData.getPaymentInfo() != null) {
            double conversionFactor = Utility.getHotelierConversionFactor(persistedMultiRoomData);
            double pgCharges = persistedMultiRoomData.getPaymentInfo().getPgCharges();
            pgCharges /= conversionFactor;
            thankYouResponse.setPgCharges(pgCharges);
        }
    }


    private void buildMyPatHeroBanner(ThankYouResponse thankYouResponse, PersistedMultiRoomData persistedMultiRoomData) {
        if(persistedMultiRoomData.getMyPatHeroBanner() != null) {
            if(thankYouResponse.getBookingDetails()!=null && BookingStatus.SUCCESS.equals(thankYouResponse.getBookingDetails().getStatus())) {
                thankYouResponse.setMyPatHeroBanner(persistedMultiRoomData.getMyPatHeroBanner());
            }
        }
    }

    /*Building hero or cashback persuasions for MyPARTNER funnel, where loyalty_offer_message is prioritized
    and calling utility method if couponInfo is available*/
    private Map<String, PersuasionResponse> buildPersuasionsMap(PersistedMultiRoomData persistedData) {
        Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
        boolean isMyPartnerRequest = (persistedData!=null) && (persistedData.getAvailReqBody()!=null) && Utility.isMyPartnerRequest(persistedData.getAvailReqBody().getProfileType(),persistedData.getAvailReqBody().getSubProfileType());
        if(isMyPartnerRequest)
        {
            if(null!=persistedData.getTotalDisplayFare() && null != persistedData.getTotalDisplayFare().getDisplayPriceBreakDown() && null != persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
                BestCoupon coupon = persistedData.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
                //If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
                logger.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
                boolean isCashbackAmtAvailable= MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey(CASHBACK_TO_WALLET);
                if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
                    buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
                }
            }
        }
        return persuasionMap;
    }

    protected abstract String getMytripActionCorpUrl(String cardType);

    protected abstract String getMytripActionB2CUrl(String cardType);

    protected abstract String getHotelDetailsRawDeepLinkUrl(PersistedMultiRoomData persistedMultiRoomData);

    protected abstract String getMytripsRawDeepLinkUrl(UserGlobalInfo userGlobalInfo, Map<String, String> expData);

    protected abstract boolean tildeRequiredInRSQ();

}
