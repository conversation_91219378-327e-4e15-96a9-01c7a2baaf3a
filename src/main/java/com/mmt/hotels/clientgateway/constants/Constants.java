package com.mmt.hotels.clientgateway.constants;

import com.mmt.hotels.model.enums.InclusionCategory;
import com.mmt.hotels.model.enums.SectionsType;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;


public class Constants {

    public static final String HORIZONTAL = "H";
    public static final String VERTICAL = "V";
	public static final String HN = "HN";
    public static final String SPACE = " ";
	public static final String TILE = "tile";
	public static final String FLEX = "flex";
	public static final String KIDS = "KIDS";
	public static final String DPT_COLLECTIONS = "DPT_COLLECTIONS";
	public static final String EXP_DATA = "expData";
	public static final String EXCLUSIVE_HOTELS = "EXCLUSIVE_HOTELS";
	public static final String REGION_SA = "SA";
	public static final String WWW_SUBDOMAIN = "https://www.";
	public static final String SA_SUBDOMAIN = "https://sa.";
	public static final String AE_SUBDOMAIN = "https://ae.";
	public static final String KEY_PAGE = "page";
	public static final String HOTEL_BOOKING_DETAIL = "hotelBookingDetail";
	public static final String BEDS = "BEDS";
	public static final String BED = "BED";

	public static final String REGION_AE = "ae";
	public static final String IS_UGC_V2 = "isUGCV2";
	public static final String SALE_CAMPAIGN_PERSUASION_APPS = "sale_campaign_persuasion_APPS";
	public static final String SALE_CAMPAIGN_PERSUASION_DT = "sale_campaign_persuasion_DT";
	public static final String BRAND = "brand";
	public static final String BRANDS = "BRANDS";
    public static final String FILTER = "filter";
	public static final String CLIENT_ANDROID = "android";
	public static final String CLIENT_IOS = "ios";
	public static final List<String> vowels = Arrays.asList("a","e", "i", "o", "u");
    public static List<String> inclusionOrderList = Arrays.asList("KIDS", InclusionCategory.ROOM_UPGRADE.getLeafCategory() ,InclusionCategory.MEAL_UPGRADE.getLeafCategory(),
			InclusionCategory.HOTEL_CREDIT.getLeafCategory(), InclusionCategory.SPA.getLeafCategory(), InclusionCategory.FOOD_AND_BEVERAGE.getLeafCategory(),
			"ZPN", "MEAL", "OTHERS", "USP");
    public static final String SEMICOLON = ";";
    public static final String ERR_CODE_API = "2";
    public static final String CORP_ID_CONTEXT = "CORP";
    public static final String IS_CORPORATE = "isCorporate";
    public static final String B2B_ID_CONTEXT = "B2B";
	public static final String BKG_DEVICE_PWA = "PWA";
	public static final String GUESTS_COUNT_THREE = "3";
	public static final String GUESTS_COUNT_FOUR = "4";
	public static final String TEN = "10";
	public static final String USER_COUNTRY = "user-country";
	public static final String ENTITY_NAME = "entity-name";
	public static final String SHORTSTAYS_FUNNEL="SHORTSTAYS";
    public static final String MOB_ID_CONTEXT = "MOB";
    public static final String FILTER_COND_FUNNEL = "funnelSource";
    public static final String FILTER_HOTEL_PRICE = "PRICE";
    public static final String FILTER_HOTEL_PRICE_BUCKET = "PRICE_BUCKET";
	public static final String FILTER_ROOMS_AND_BEDS = "ROOMS_AND_BEDS";
    public static final String FILTER_PRICE_MANUAL = "HOTEL_PRICE_MANUAL";
	public static final String FILTER_SPACE = "SPACE";
    public static final String PRICE_CHECKBOX = "PRICE_CHECKBOX";
	public static final String COUNTRY = "country";
	public static final String JOURNEY_ID = "journeyId";
	public static final String SITE_DOMAIN = "siteDomain";
	public static final String TYPE_CITY = "city";
	public static final String FILTER_PRICE_BUCKET_HES = "HOTEL_PRICE_BUCKET";
	public static final String HOTEL_PRICE = "HOTEL_PRICE";
    public static final String FILTER_POPULAR = "POPULAR";
    public static final String PAGE_SUGGESTED_FOR_YOU = "SUGGESTED_FOR_YOU";
    public static final String SIGNATURE_AMENITIES = "SIGNATURE_AMENITIES_NEW_CATEGORY";
    public static final String FILTER_DEALS = "DEALS";
    public static final String FILTER_PREVIOUSLY_USED = "PREVIOUSLY_USED";
    public static final String FILTER_PREFERRED = "PREFERRED_RATES_OrgId_";
    public static final String PRICE_TITLE = "Price";
    public static final String PRICE_EXP = "PDO";
    public static final String PRICE_PN = "PN";
    public static final String PRICE_PNT = "PNT";
    public static final String PRICE_PRN = "PRN";
    public static final String PRICE_PRNT = "PRNT";
    public static final String PRICE_TP = "TP";
    public static final String PRICE_TPT = "TPT";
    public static final String RULE_BASED_FILTER_SETTINGS_ENABLED = "ruleBasedFilterSettings";

	public static final  String N = "N";
	public static final String ANDROID = "ANDROID";
	public static final String DEVICE_OS_ANDROID = "ANDROID";

    public static final String DEVICE_IOS = "IOS";
    public static final String DEVICE_OS_IOS = "IOS";
    public static final String DEVICE_MSITE = "msite";
    public static final String DEVICE_OS_DESKTOP = "desktop";
    public static final String DEVICE_OS_PWA = "pwa";
    public static final String RECOMMENDED_HOTELS_SECTION="RECOMMENDED_HOTELS";
	public static final String HG_HORIZONTAL_HEADING="Similar properties preferred by our top partners";
	public static final String RECOMMENDED_HOTELS_HEADING="Recommended for You";
	public static final String MYPARTNER_HIGH_GRASP_HOTELS="mypartner_control_HG";
	public static final String MYPARTNER_HG_NOT_SHOW="HG_NOTSHOW";
	public static final String EXCLUSIVE_DEAL_HEADING="Exclusive Hotel Deals since you recently booked a flight";
	public static final String NEARBY_HOTELS_SECTION = SectionsType.NEARBY_HOTELS.name();
	public static final String OTHER_ALTACCO_HOTELS_SECTION="OTHER_ALT_ACCO_HOTELS";
	public static final String NON_ALTACCO_HOTELS_SECTION="NON_ALT_ACCO_HOTELS";
	public static final String HEADER_AKAMAI="X-Akamai-Edgescape";
	public static final String COMMA = ",";
	public static final String NEXT_LINE = "\n";
	public static final String COMMA_SPACE = ", ";
	public static final String FULLSTOP_SPACE = ". ";
	public static final String SPACE_X_SPACE = " x ";
	public static final String HEADER_COUNTRY_CODE="country_code";
	public static final String HEADER_CITY = "city";
	public static final String USER_CURRENCY = "user-currency";
	public static final String VISITOR_ID = "visitor-id";
	public static final String EQUI = "=";
	public static final String QUESTION = "?";
	public static final String AMP = "&";
	public static final String DEFAULT_DOMAIN = "DEFAULT";
	public static final String DEFAULT_CUR_INR = "INR";
	public static final String DEFAULT_SITE_DOMAIN= "IN";
	public static final String DEFAULT_LANGUAGE= "eng";
	public static final String ENGLISH = "English";
	public static final String TRAFFIC_TYPE_CMP = "CMP";
	public static final String REGION_CODE ="region_code";
	public static final String LOB_DOM_FLIGHTS = "df";
	public static final String LOB_INTL_FLIGHTS = "if";
	public static final String USER_LOB_LAST_BOOKED_SC = "user_lob___last_booked_sc___NA___P1D___v1";
	public static final String DEVICE_LOB_LAST_BOOKED_SC = "device_lob___last_booked_sc___NA___P1D___v1";
	public static final String USER_LOB_LAST_BOOKED_TS =  "user_lob___last_booked_ts___NA___PT30S___v1";
	public static final String DEVICE_LOB_LAST_BOOKED_TS =  "device_lob___last_booked_ts___NA___PT30S___v1";
	public static final String ANDROID_MCID = "mcid";
	public static final String COMMON_MCID = "usr-mcid";
	public static final String PROFILE_TYPE_CTA = "CTA";
	public static final String SUBPROFILE_CTA = "CTA";
	public final static String AFFILIATE_ID = "AFFILIATE_ID.";
	public static final String APPLICATION_ID = "APPLICATIONID";
	public static final String DOT = ".";
	public static final String DOT_SEPARATOR = " • ";
	public static final String DEVICE_ID = "deviceid";
	public static final Set<String> HYDRA_BLOCKED_DEVICEID_LIST = new HashSet<>(Arrays.asList("00000000-0000-0000-0000-000000000000", "Opera", "Firefox", "Safari", "Internet Explorer", "MSIE", "Chrome", "PWA", "null"));
	public static final String COSMOS = "COSMOS";
	public static final String CBINTERNAL = "CBINTERNAL";
	public static final String CHANNEL_FKPWA = "FKPWA";
	public static final String QUE_MARK = "?";
	public static final String CORRELATIONKEY = "correlationKey";
	public static final String CK_CORRELATION_KEY = "ck"; // IN this format correlation key is coming from client
	public static final String KEY_QUERY = "query";
	public static final String KEY_API = "api";
	public static final String CONTEXT_TYPE = "contextType";
	public static final String ALL = "ALL";
	public static final String EVERYONE = "EVERYONE";
	public static final String REQ_PARAM_TYPE="type";
	public static final String XSS_DISALLOWED_CHARACTERS_IN_URL = "[(`;<>)'\"]"; //this is used as regex so [ and ] are allowed
	public static final String PROFILE_CORPORATE = "BUSINESS";
	public static final String LOGIN_INFO_TYPE_FK_IDNTY = "FK_IDNTY";
//	public static final String USER_LOB_LAST_BOOKED_TS =  "user_lob::last_booked_ts::NA::PT30S::v1";
//	public static final String DEVICE_LOB_LAST_BOOKED_TS =  "device_lob::last_booked_ts::NA::PT30S::v1";
	public static final String HYDRA_SEGEMNET_SOCKET_TIME_OUT_ERROR = "HYDRA_SEGEMNET_API_SOCKET_TIME_OUT_ERROR IN {} MS";
	public static final String LOWEST_RATE_SEGMENT_KEY = "LOWEST_RATE_SEGMENT_ID";
	public static final String B2C = "B2C";
	public static final String  REGION ="region";
	public static final String CURRENCY="currency";
	public static final String LANGUAGE="language";
	public static final String EMPTY_STRING = "";
	public static final String CONTEXT_PATH = "/clientbackend";
	public static final String LOGIN_TYPE_MOBILE = "MOBILE";
	public static final String CLIENT = "client";
	public static final String VERSION = "version";
	public static final String SRC_CLIENTBACKEND = "CLIENTBACKEND";
	public static final String DOM_COUNTRY = "IN";
	public static final String DOC_TYPE_PAN = "PAN";
	public static final String ID_CONTEXT = "idContext";
	public static final String WORK_FLOW_ID = "workflowId";

	public static final String TOTAL_AMOUNT_KEY = "TOTAL_AMOUNT";
	public static final String PRICE_AFTER_DISCOUNT = "PRICE_AFTER_DISCOUNT";

	public static final String FLEXI_CANCEL_TOTAL_AMOUNT_KEY = "FLEXI_CANCEL_TOTAL_AMOUNT";
	public static final String FLEXI_CANCEL_AMOUNT = "FLEXI_CANCEL_AMOUNT";
	public static final String CHARITY = "CHARITY";
	public static final String AMOUNT_YOU_PAYING_NOW_KEY = "AMOUNT_YOU_PAYING_NOW_KEY";
	public static final String BASE_FARE_KEY = "BASE_FARE";
	public static final String FLEXI_CANCEL_BASE_FARE_KEY = "FLEXI_CANCEL_BASE_FARE";
	public static final String BASE_FARE_WITH_TAX_KEY = "BASE_FARE_WITH_TAX";
	public static final String TOTAL_DISCOUNT_KEY = "TOTAL_DISCOUNT";
	public static final String PARTNER_COMMISSION_KEY = "PARTNER_COMMISSION";
	public static final String TOTAL_INSURANCE = "TOTAL_INSURANCE";
	public static final String TOTAL_SME = "TOTAL_SME";
	public static final String TCS_AMOUNT = "TCS_AMOUNT";
	public static final String MMT_DISCOUNT_KEY = "MMT_DISCOUNT";
	public static final String CDF_DISCOUNT_KEY = "CDF_DISCOUNT";

	public static final String HOTELIER_DISCOUNT_LABEL_CORP = "myBiz Special Discount";

	public static final String INSURANCE_AMOUNT = "INSURANCE_AMOUNT";
	public static final String SME_AMOUNT = "SME_AMOUNT";
	public static final String BLACK_DISCOUNT_KEY = "BLACK_DISCOUNT";
	public static final String LONGSTAY_BENEFITS = "LONGSTAY_BENEFITS";
	public static final String BLACK_SEGMENT_IDENTIFIER = "BLACK";
	public static final String SELECT_SEGMENT_IDENTIFIER = "SELECT";

	public static final String WALLET_KEY = "WALLET_DISCOUNT";
	public static final String PRICE_AFTER_DISCOUNT_KEY = "PRICE_AFTER_DISCOUNT";
	public static final String HOTEL_DISCOUNT_KEY = "HOTEL_DISCOUNT";
	public static final String TDS_KEY = "TDS";
	public static final String TAXES_LABEL = "Taxes & Service fees";
	public static final String TAXES_KEY = "TAXES";
	public static final String GST_LABEL = "Hotel GST";
	public static final String HOTEL_TAX_LABEL = "Hotel Taxes";
	public static final String HOTEL_TAX_KEY = "HOTEL_TAX";
	public static final String SERVICE_FEES_LABEL = "Service Fees";
	public static final String SERVICE_FEES_KEY = "SERVICE_FEES";
	public static final String SERVICE_FEES_GST_KEY = "SERVICE_FEES_GST";
	public static final String SERVICE_CHARGE_LABEL = "Service Charge";
	public static final String SERVICE_CHARGE_KEY = "SERVICE_CHARGE";
	public static final String TAX_BREAKUP_KEY = "TAX_BREAKUP_KEY_";
	public static final String AFFILIATE_FEES_LABEL = "Affiliate Fees";
    public static final String AFFILIATE_FEES_KEY = "AFFILIATE_FEES";
    public static final String SERVICE_FEES_REVERSAL_KEY = "SERVICE_FEES_REVERSAL";
    public static final String SERVICE_FEES_REVERSAL_LABLE = "Reversal of Service Fee";
    public static final String EFFECTIVE_COUPON_APPLIED_KEY = "EFFECTIVE_COUPON_APPLIED";
    public static final String EFFECTIVE_COUPON_APPLIED_LABLE = "Effective Coupon Applied";

    /*
    thankyou constants
     */
	public static final String AMOUNT_LABEL_MMT_WALLET="MMT_WALLET";
	public static final String AMOUNT_LABEL_OTHER_PAYMODES="OTHER_PAYMODES";
	public static final String AMOUNT_LABEL_TOTAL_AMOUNT="TOTAL_AMOUNT";
	public static final String AMOUNT_LABEL_PARTIAL_AMOUNT_LEFT="PARTIAL_AMOUNT_LEFT";
	public static final String AMOUNT_LABEL_PARTIAL_AMOUNT_PAID="PARTIAL_AMOUNT_PAID";
	public static final String AMOUNT_LABEL_AMOUNT_CARD="AMOUNT_CARD";
	public static final String AMOUNT_LABEL_REMAINING_AMOUNT="REMAINING_AMOUNT";
	public static final String AMOUNT_LABEL_AMOUNT_HOTEL="AMOUNT_HOTEL";
	public static final String AMOUNT_LABEL_APPROX_IN="APPROX_IN";
	public static final String PAGE_CONTEXT_THANK_YOU = "ThankYou";
	public static final String ONLY = "only";
	public static final String MEAL_PLAN_CODE_ROOM_ONLY="EP";
	public static final String MEAL_PLAN_CODE_BED_ONLY="BD";
	public static final String MEAL_PLAN_CODE_ACC_ONLY="AO";
	public static final String MEAL_PLAN_CODE_BREAKFAST="CP";
	public static final String MEAL_PLAN_CODE_BREAKFAST_LUNCH="TMAP";
	public static final String MEAL_PLAN_CODE_BREAKFAST_DINNER="SMAP";
	public static final String MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER="MAP";
	public static final String MEAL_PLAN_CODE_ALL_MEALS="AP";
	public static final String MEAL_PLAN_CODE_ALL_MEALS_AI="AI";

	public static final String TWO_MEAL_AVAIL = "TWO_MEAL_AVAIL";
	public static final String ALL_MEAL_AVAIL = "ALL_MEAL_AVAIL";
	public static final String SPECIALDEALS = "SPECIALDEALS";

	public static final String LOB_DONATION="DONATION";
	public static final String RSQ_SPLITTER="e";
	public static final String RSQ_ROOM_SPLITTER = "~";
	public static final String BOOKING_NON_INSTANT_CONFIRMATION = "NON_INSTANT";
	public static final String BOOKING_PNR_KEY = "PNR";
	public static final String BOOKING_STATUS_SUCCESS = "success";
	public static final String SUCCESS = "success";
	public static final String FAILURE = "failure";
	public static final String YET_TO_BE_GENERATED= "Yet To Be Generated";
	public static final int HOTEL_CATEGORY_SELECTION_ALGO_VERION_1 = 1;

	public static final String UNDERSCORE = "_";
	public static final String DOUBLE_UNDERSCORE = "__";
	public static final String PIPE = "|";
	public static final String BOOKING_ID = "bookingId";
	public static final String PAGE_CONTEXT_DETAIL = "DETAIL";
	public static final String PAGE_CONTEXT_REVIEW = "REVIEW";
	public static final String PAGE_CONTEXT_LISTING = "LISTING";
	public static final String auth_code = "authCode";
	public static final String SRC_CLIENT = "srcClient";

	public static final String LATEST_FIRST = "Latest first";
	public static final String HELPFUL_FIRST = "Helpful first";
	public static final String POSITIVE_FIRST = "Positive first";
	public static final String NEGATIVE_FIRST = "Negative first";
	public static final String OVERALL = "OVERALL";

	public static final String RUSH_DEAL_MMT_FILTER_TITLE_TEXT = "RUSH_DEAL_MMT_FILTER_TITLE_TEXT";
	public static final String FREE_KIDS_FILTER_TITLE = "FREE_KIDS_FILTER_TITLE";
	public static final String RUSH_DEALS_MMT_EXP_KEY = "mmt.backend.hotel.default.default.default.rush_deal";

	public static final String ROOMTYPE = "roomType";

	public static final String MOST_RESTRICTED_POLICY = "Y";
	public static final String SPCL_CAT_TYPE_LABEL = "LABEL";
	public static final String HEADER_REGION = "region";	
	public static final String HEADER_LANGUAGE = "language";
	public static final String HEADER_CURRENCY = "currency";
	public static final String DEFAULT_CUR_USD = "USD";
	public static final String SOURCE_CON = "srCon";
	public static final String SOURCE_CLIENT = "srcClient";
	public static final String DES_CON = "countryCode";
	public static final String LOCATIONID = "locationId";
	public static final String LOCATIONTYPE = "locationType";
	public static final String CURRENCYCODE = "currencyCode";
	public static final String CONTENT_LENGTH = "content-length";
	public  static final String BST_SAFETY_FILTER ="MySafety - Safe and Hygienic Stays";
	public  static  final String UNRATED_SR="Unrated";
	public static final String TRAFFIC_SOURCE_SEO = "seo";
	public static final String TRAFFIC_SOURCE_META = "META";
	public static final String TRAFFIC_SOURCE_SEM = "SEM";
	public static final String TRAFFIC_SOURCE_PHONEPE = "PHONEPE";
	public static final String PERSUASION_TYPE_SEO = "SEO";

	public static final String KID_FRIENDLY = "Kid Friendly";
	public static final String COUPLE_FRIENDLY = "Couple Friendly";
	public static final String STAYCATION = "Staycation Deals";
	public static final String GREATVALUE = "Great Value Deals";
	public static final String MyBiz_Assured = "MyBiz Assured";
	public static final String MYBIZ_RECOMMENDED_INTL_SECTION = "MYBIZ_RECOMMENDED_INTL";
	public static final String APPLY_IN_POLICY_COR_FILTER_EXP = "applyInPolicyCorpFilter";
	public static final String IN_POLICY_FILTER_REMOVED_HEADING_MYBIZ_ASSURED = "IN_POLICY_FILTER_REMOVED_HEADING_MYBIZ_ASSURED";
	public static final String IN_POLICY_FILTER_REMOVED_HEADING = "IN_POLICY_FILTER_REMOVED_HEADING";
	public static final String MMT_Assured = "MMT Assured";
	public static final String MY_BIZ_ASSURED_SECTION = "MYBIZ_ASSURED";
	public static final String NON_MY_BIZ_ASSURED_SECTION = "NONMYBIZ_ASSURED";
	public static final String NOT_MYBIZ_ASSURED_SHOWN = "NOT_MYBIZ_ASSURED_SHOWN";
	public static final String MYBIZ_SIMILAR_HOTELS = "MYBIZ_SIMILAR_HOTELS";
	public static final String MYBIZ_SIMILAR_TO_DIRECT_HOTEL= "MYBIZ_SIMILAR_TO_DIRECT_HOTEL";
	public static final String MYBIZ_ASSURED_NEW = "MyBiz_Assured_New";
	public static final String MYBIZ_ASSURED = "MyBiz Assured";
	public static final String KEY_TRAVEL_REASONS = "travelReasons";
	public static final String KEY_SKIP_APPROVAL_REASONS = "skipApprovalReasons";
	public static final String KEY_GUEST_HOUSE_REASONS = "guestHouseReasons";
	public static final String GUEST_HOUSES_API = "cg/guest-houses";
	public static final String TRANSACTION_KEY = "transactionKey";
	public static final String MYPARTNER_ASSURED_FILTER_VALUE = "MyPartner";
	public static final String MYPARTNER_ASSURED = "MYPARTNER_GST_ASSURED";

	public static final String CORPBUDGET_DIRECT_HOTEL= "DIRECT_HOTEL";

	public static final String ADDITIONAL_FEE_SUBTEXT_SEPARATOR = " x ";
	public static final String ADDITIONAL_FEE_SUBTEXT_SPACE = " ";
	public static final String ADDITIONAL_FEE_SUBTEXT_LINE_SEPARATOR = " \n ";
	public static final String ADDITIONAL_FEE_SUBTEXT_ROOMS = "Rooms";
	public static final String ADDITIONAL_FEE_SUBTEXT_ROOM = "Room";
	public static final String ADDITIONAL_FEE_PER_STAY_CHARGE = "per stay";
	public static final String ADDITIONAL_FEE_PER_ADULT_CHARGE = "per adult";
	public static final String ADDITIONAL_FEE_PER_CHILD_CHARGE = "per child";
	public static final String ADDITIONAL_FEE_PER_ROOM_CHARGE = "per room";
	public static final String ADDITIONAL_FEE_PER_NIGHT_CHARGE = "per night";

	public static final String STYLE="style";

	public static final String CTRIP_INS = "INSTANT";
	public static final String CTRIP_NONINS="NON_INSTANT";
    public static final String EARLY_CHECKIN_CATEGORY = "Early Checkin";

    public static final String SELLABLE_ROOM_TYPE = "Room";
    public static final String SELLABLE_BED_TYPE = "Bed";
	public static final String SELLABLE_ENTIRE_TYPE = "ENTIRE";
    public static final String LISTING_TYPE_ENTIRE = "entire";
    public static final String AMENITIES_OPEN_BRACE = "(";
    public static final String AMENITIES_CLOSING_BRACE = ")";
    public static final String HYPEN = "-";
	public static final String HYPHEN_SPACE = " - ";

    public static final String SUPPLIER_INGO = "INGO";
    public static final String HEADER_LAT = "lat";
    public static final String HEADER_LONG = "long";
	public static final String PAS = "PAS";
	public static final String PAH = "PAH";
	public static final String PAH_WITHOUT_CC="PAH_WITHOUT_CC";
	public static final String PAH_WITH_CC = "PAH_WITH_CC";
	public static final String TRANSLATION_CACHE = "translation-cache";

	public static final String TITLE_MISS = "Miss";
	public static final String TITLE_MS = "Ms";
	public static final String TITLE_MRS = "Mrs";

	public static final String HEADER_CONTENT_TYPE = "Content-Type";
	public static final String HEADER_CONTENT_APPLICATION_JSON="application/json";
	public static final String HEADER_ACCEPT_ENCODING = "Accept-Encoding";
	public static final String HEADER_GZIP = "gzip";

	public static final String HEADER_ACCEPT_TYPE = "Accept";
	public static final String HEADER_CONTENT_APPLICATION_JSON_UTF_8="application/json; charset=UTF-8";

	public static final String HEADER_CONTENT_MULTIPART_FORM_DATA="multipart/form-data";

	public static final String BLACK_INCLUSION_IDENTIFIER = "BLACK";
	public static final String LOCATION_PERSUAION_PLACEHOLDER_ID = "PC_MIDDLE_2";
	public static final String  LOCATION_PERSUAION_HN_PLACEHOLDER_ID = "PC_HN_2";
	public static final String LOCATION_PERSUAION_2_PLACEHOLDER_ID = "PC_MIDDLE_2_1";
	public static final String LOCATION_PERSUAION_PLACEHOLDER_ID_APP = "PLACEHOLDER_CARD_M1";
	public static final String SEO_TEXT_PERSUASION_PLACEHOLDER_ID = "PC_MIDDLE_10";
	public static final String PLACEHOLDER_CARD_SEO = "PLACEHOLDER_CARD_SEO";
	public static final String PLACEHOLDER_PRICE_BOTTOM = "PLACEHOLDER_PRICE_BOTTOM";
	public static final String PLACEHOLDER_SELECT_TOP_R1 = "PLACEHOLDER_SELECT_TOP_R1";
	public static final String PC_SELECT_RIGHT_1 = "PC_SELECT_RIGHT_1";
	public static final String PLACEHOLDER_PRICE_BOTTOM_M1 = "PLACEHOLDER_PRICE_BOTTOM_M1";

	public static final String AMENITIES_PLACEHOLDER_ID = "PC_MIDDLE_6";
	public static final String AMENITIES_PLACEHOLDER_ID_HN = "PC_HN_3";
	public static final String AMENITIES_PLACEHOLDER_ID_APP = "PLACEHOLDER_CARD_M2";

	public static final String TOOL_TIP_SAFETY = "SAFETY";
	public static final String TOOL_TIP_GT = "GTA"; // Ground Transport Access
	public static final String TOOL_TIP_OOP = "OOP";
	public static final String TOOL_TIP_INDIANNESS = "TOOL_TIP_INDIANNESS";
	public static final String TOOL_TIP_GST_ASSURED= "TOOL_TIP_MYP_GST_ASSURED";
	public static final String LOVED_BY_DEVOTEES = "LOVED_BY_DEVOTEES";
	public static final String LOVED_BY_INDIANS = "LOVED_BY_INDIANS";
	public static final String PERSUASION_KEY = "persuasionKey";
	public static final String TOOL_TIP_BNPL_AVAIL = "BNPL_AVAIL";
	public static final String TOOL_TIP_FCZPN = "FCZPN";
	public static final String TOOL_TIP_FC = "FREE_CANCELLATION";
	public static final String TOOL_TIP_HOSTEL_STAY = "HOSTEL";
	public static final String TOOL_TIP_VILLA_STAY = "VILLA";
	public static final String TOOL_TIP_HOMESTAY_STAY = "HOMESTAY";
	public static final String TOOL_TIP_COTTAGE_STAY = "COTTAGE";
	public static final String TOOL_TIP_APARTMENT_STAY = "APARTMENT";
	public static final String TOOL_TIP_TYPE_PROPERTY_BENEFITS = "PROPERTY_BENEFITS";
	public static final String LAST_BOOKED_HOTELS = "LAST_BOOKED_HOTELS";
	public static final String RECENTLY_VIEWED_HOTELS = "RECENTLY_VIEWED_HOTELS";
	public static final String ONE_CLICK = "ONE_CLICK";

	public  static  final String WORKFLOW_PENDING="pending";
	public static final String MOBGEN_CACHE = "mobgen-cache";

	public static final String CANCELLATION_TYPE_FC = "FC";
	public static final String FREE_CANCELLATION = "FREE_CANCELLATION";
	public static final String NON_REFUNDABLE = "NON_REFUNDABLE";
	public static final String FOOD_AND_DINING_REVAMP_TITLE = "Food and Dining";
	public static final String RESTAURANTS_TITLE = "Restaurants";
	public static final String OCCASSION_ENABLED_APP_VERSION = "9.4.8";

	// food dining icon keys
	public static final String VEG_ICON = "ic_veg";

	public static final String CONTRACTED_FARE = "CONTRACTED_FARE";
	public static final String HOTEL_CLOUD = "HOTEL_CLOUD";
	public static final String HOTEL_CLOUD_TITLE = "Hotel Cloud";

	public static final String HOTEL_CLOUD_DESKTOP_PERSUASION_ICON = "https://promos.makemytrip.com/mybiz/hotelcloudlogo.png";
	public static final String HOTEL_CLOUD_PERSUASION_ICON = "https://promos.makemytrip.com/mybiz/hotelcloudapp.png";
	public static final String HOTEL_CLOUD_TITLE_TEXT = "HOTEL_CLOUD_TITLE_TEXT";

	// Location Persuasion Constants (HTL-64116)
	public static final String UGC_LOCATION = "UGC_LOCATION";
	public static final String UGC_PERSUASION_KEY = "persuasion";
	
	public static final String POST_BOOKING_CARD = "POST_BOOKING_CARD";
	public static final String CANCELLATION_TYPE_FCZPN = "FCZPN";
	public static final String CANCELLATION_TYPE_NR = "NR";
	public static final String FREE_BREAKFAST = "FREE_BREAKFAST";

	public static final String DEFAULT = "DEFAULT";
	public static final String MMT_SUPER_PACKAGE = "MMT_SUPER_PACKAGE";
	public static final String MMT_OCCASION_PACKAGE = "MMT_OCCASION_PACKAGE";

	public static final String SUPER_PACKAGE_TYPE = "SUPER_PACKAGE";
	public static final String LOS_BENEFIT_IMAGE_URL = "https://promos.makemytrip.com/Hotels_product/Details/Packages/Final/longstay-image-url.png";
	public static final String LOS_BENEFIT_ICON_URL = "https://promos.makemytrip.com/Hotels_product/campaign/los_dot.png";

	public static final String MEAL_UPGRADE = "meal_upsell_<{mealPlan}>_Old";
	public static final String PRICE_TOP = "price_top";
	public static final String TOP_RIGHT = "topRight";
	public static final String RIGHT_BOTTOM = "rightBottom";
	public static final String TEXT_WITH_BG_IMAGE = "TEXT_WITH_BG_IMAGE";
	public static final String COLOR_A47313 = "#A47313";
	public static final String SMALL = "SMALL";
	public static final String SUPER_PACKAGE = "Super Package";
	public static final String BREAKFAST = "BREAKFAST";
	public static final String LUNCH = "LUNCH";
	public static final String DINNER = "DINNER";
	public static final String MEAL = "MEAL";
	public static final String USP = "USP";
	public static final String ZPN = "ZPN";
	public static final String OTHERS = "OTHERS";
	public static final String TOOL_TIP_MYBIZ_ASSURED = "TOOL_TIP_MYBIZ_ASSURED";
	public static final String TOOL_TIP_MMT_VALUE_STAY = "TOOL_TIP_MMT_VALUE_STAY";
    public static final String VALUE_STAY_TAG_TITLE = "VALUE_STAY_TAG_TITLE";
    public static final String MMT_VALUE_STAYS = "MMT Value Stays";
	public static final String VALUE_STAY_TAG_TITLE_REVIEW = "VALUE_STAY_TAG_TITLE_REVIEW";
	public static final String VALUE_STAY_TAG_TITLE_THANK_YOU = "VALUE_STAY_TAG_TITLE_THANK_YOU";
	public static final String FUNNEL_SOURCE_HOMESTAY = "HOMESTAY";
	public static final String FUNNEL_SOURCE_HOMESTAY_NEW = "HOMESTAY_HTL";
	public static final String BEDROOM_COUNT_AVAILABLE = "AABI";
	public static final String FUNNEL_SOURCE_HOTELS = "HOTELS";
	public static final String TRAFFIC_SOURCE_FLIGHTS_THANKYOU_PAGE = "FLIGHTSTY";
	public static final String TRAFFIC_SOURCE_VISTARA_FLIGHTS = "VISTARA_FLIGHTSTY";
	public static final String VISTARA_FILTER_VALUE = "Vistara Deal";

	public static final String STAR_RATING = "STAR_RATING";
	public static final String STAR_RATING_4 = "4";

	public static final String STAR_RATING_5 = "5";
	public static final String TRAFFIC_SOURCE_BUSES_THANKYOU_PAGE = "BUSESTY";
	public static final String TRAFFIC_SOURCE_TRAINS_THANKYOU_PAGE = "TRAINSTY";
	public static final String ZONE = "zone";
	public static final String FUNNEL_DAYUSE = "DAYUSE";

	public static final String FUNNEL_SOURCE_SHORTSTAYS = "SHORTSTAYS";
	public static final String AB_EXPT_DISABLE_AMENITIES = "disableAmenities";
	public static final String AB_EXPT_DISABLE_SAVE_VALUE = "disableSaveValue";
	public static final String FUNNEL_SOURCE_DAYUSE = "DAYUSE";
	public static final String FUNNEL_SOURCE_GROUP_BOOKING = "GROUP";

	public static final String FUNNEL_SOURCE_GETAWAY = "GETAWAY";
	public static final String MORE_FILTERS = "MORE_FILTERS";
	public static final String SUGGESTED_FILTERS = "SUGGESTED_FILTERS";
	public static final String OTHER_FILTER_CATEGORY = "OTHER_FILTER_CATEGORY";
	public static final String FUNNEL_SOURCE_CORPBUDGET = "CORPBUDGET";

	// mypartner segment checks for persuasions
	public static final String MYPARTNER_SEGMENT_ID = "1180";
	public static final String MYPARTNER = "MYPARTNER";
	public static final String ROOM_CODE = "roomCode";

	public static final String DISCOUNT_THRESHOLD = "discountThreshold";
	public static final String DISCOUNT_THRESHOLD_PERCENT = "discountPercentThreshold";

	public static final String LUXE_LOCATION_ID = "SFINLUX";
	public static final String LUXE_LOCATION_TYPE = "storefront";
	public static final String IS_LUXE_ONLY_FILTER = "isLuxOnlyFilter";
	public static final String TOOL_TIP_LUXE = "TOOL_TIP_LUXE";
	public static final String IMAGE_TYPE = "IMAGE";
	public static final String VIDEO_TYPE = "VIDEO";
	public static final String BOLD_TYPE = "BOLD";
	public static final String NEW_DETAIL_PAGE = "newDetailPage";
	public static final String DISABLE_HOST_CHAT = "disableHostChat";
	public static final String ENABLE_HOST_CALLING = "hostcallingaa";
	public static final String SHOW_OCC_PACKAGE = "showOccassionPackagesPlan";
	public static final String FOOD_DINING_REVAMP = "mealsimplify";
	public static final String SHOW_MANDATORY_CHARGES_DH = "DHDetMandCharge";
	public static final String NEW_DETAIL_PAGE_DESKTOP_EXP = "NDD";
	public static final String HVC = "highValueCall";
	public static final String NEW_BLACK_DEAL = "MBDTC";
	public static final String LUXE_ICON_NEW_APP = "https://promos.makemytrip.com/Growth/Images/B2C/mmtluxe_details_page_new.png";
	public static final String NEW_SELECT_ROOM_PAGE = "newSelectRoomPage";
	public static final String Per_Night = "Per Night";
	public static final String per_night = "per night";
	public static final String FOOD_DINING_TRAILING_ICON_URL  = "https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png";
	public static final String LUXE_ICON_DESKTOP = "https://promos.makemytrip.com/Hotels_product/Luxe/DT_Listing_Luxe2x.png";
	public static final String LUXE_ICON_APPS = "https://promos.makemytrip.com/Hotels_product/Luxe/Detail_luxe2x.png";
	public static final String EXCLUSIVE_DEAL_IMAGE_URL= "https://promos.makemytrip.com/images/highlighted/exclusive-deal.png";
	public static final String EXCLUSIVE_DEAL_STYLE_CLASS= "exclTag";
	public static final String EXCLUSIVE_DEAL_TAG= "exclusiveDealTag";
	public static final String ALL_INCLUSIVE_PLAN_EXPERIMENT="AIP";
	public static final String ALL_INCLUSIVE_TRANSFER_EXPERIMENT="APT";
	public static final String ENABLE_LOCATION_RECOMMENDATION_EXP="enableLocationRecommendation";
	public static final String HOMESTAY_PERSUASION_ALC="ALC";
	public static final String PRICE_DISPLAY_OPTION = "PDO";
	public static final String PER_NIGHT = "PN";
	public static final String MYPARTNER_CONTROL_RB_RC = "mypartner_control_RB_RC";// My Partner control for Recently Booked and Recently Clicked
	public static final String MYPARTNER_EXCLUSIVE_DEAL = "mypartner_exclusive_deal";// My Partner pokus for controlling exclusive deal tag
	public static final String LUXE_PACKAGE = "LUXE_PACKAGE";
	public static final String LUXE = "LUXE";
	public static final String NON_LUXE_PACKAGE = "NON_LUXE_PACKAGE";
	public static final String MMT_LUXE_PACKAGE = "MMT_LUXE_PACKAGE";
	public static final String MMT_NON_LUXE_PACKAGE = "MMT_NON_LUXE_PACKAGE";
	public static final String LUXURY_HOTELS = "luxury_hotels";
	public static final String PREMIUM_CONTEXT = "Premium_context";
	public static final String BUDGET_CONTEXT = "Budget_context";
	public static final String FUNNEL_SOURCE_MYPARTNER = "myPartner";
	public static final String FUNNEL_SOURCE = "funnel";

	public static final String SELECT_ROOM_BANNER_TYPE = "RatePlanScroll";
	public static final String SELECT_ROOM_BANNER_BG_COLOR = "#CFEEE5";
	public static final String SELECT_ROOM_BANNER_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Luxe/Tip3x.png";

	public static final String GUESTS_LOVE = "GUESTS_LOVE";
	public static final String ICON_TYPE_LIGHTNING = "LIGHTNING";
	public static final String EXP_HOTEL_ALT_ACCO_FILTER = "HAFC";
	public static final String CLIENT_DESKTOP = "DESKTOP";
	public static final String VIEW_TYPE_FLEX = "flex";
	public static final String VIEW_TYPE_CHECKBOX = "checkbox";
	public static final String EXP_TRUE_VALUE = "T";
	public static final String HOMESTAY_ICON = "https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/Homestay_icon.png";
	public static final String PROPERTY_TYPE_HOMESTAY = "Homestay";
	public static final String DAYUSE_LATE_CHECKOUT = "DAYUSE_LATE_CHECKOUT";
	public static final String CHECKOUT_MSG = "CHECKOUT_MSG";

	public static final String CHECK_IN_OUT = "Check In/out";

	public static final String TRUE = "true";
	public static final String FALSE = "false";
	public static final String PROPERTY_TYPE_HOSTEL = "Hostel";
	public static final String PROPERTY_TYPE_APPARTMENT ="Apartment";
	public static final String DH_COUNTRY_CODE = "IN";
	public static final String DH_HOTEL_CURRENCY = "INR";
	public static final String MASKED_PROPERTY_NAME="mpn";
	public static final String REQUISITION_ID = "requisitionID";
	public static final String MYBIZ_FLOW_IDENTIFIER = "myBizFlowIdentifier";
	public static final String OPEN_ROOM_DEEP_LINK_URL = "select_room";


	//filter processing components for time logging
	public static final String PROCESS_FILTER_REQUEST_PROCESSOR="P_FILTER_REQUEST_PROCESSOR";
	public static final String PROCESS_DOWNSTREAM_FILTER_HES_CALL="D_FILTER_HES_CALL";
	public static final String PROCESS_DOWNSTREAM_TREELS_FILTER_HES_CALL="D_TREELS_FILTER_HES_CALL";
	public static final String PROCESS_DOWNSTREAM_EVALUATE_FILTER_RANK_CALL="D_EVALUATE_FILTER_RANK_CALL";
	public static final String PROCESS_FILTER_PMS_CONFIGURATION="P_FILTER_PMS_CONFIGURATION";
	public static final String PROCESS_FILTER_RESPONSE_PROCESS="P_FILTER_RESPONSE_PROCESS";

	//search-hotels components for time logging
	public static final String PROCESS_SEARCH_REQUEST_PROCESS="P_SEARCH_REQUEST_PROCESS";
	public static final String PROCESS_SEARCH_COMMON_REQUEST_PROCESS="P_SEARCH_COMMON_REQUEST_PROCESS";
	public static final String PROCESS_SEARCH_RESPONSE_PROCESS="P_SEARCH_RESPONSE_PROCESS";

	//search-rooms components for time logging
	public static final String PROCESS_DETAIL_REQUEST_PROCESS="P_DETAIL_REQUEST_PROCESS";
	public static final String PROCESS_DETAIL_COMMON_REQUEST_PROCESS="P_DETAIL_COMMON_REQUEST_PROCESS";
	public static final String PROCESS_DETAIL_RESPONSE_PROCESS="P_DETAIL_RESPONSE_PROCESS";

	//avail-rooms components for time logging
	public static final String PROCESS_REVIEW_REQUEST_PROCESS="P_REVIEW_REQUEST_PROCESS";
	public static final String PROCESS_REVIEW_COMMON_REQUEST_PROCESS="P_REVIEW_COMMON_REQUEST_PROCESS";

	//emi-details components for time logging
	public static final String PROCESS_EMI_DETAILS_DOWNSTREAM_PROCESS = "P_EMI_DETAILS_DOWNSTREAM_PROCESS";
	public static final String PROCESS_REVIEW_RESPONSE_PROCESS="P_REVIEW_RESPONSE_PROCESS";


	public static final String EXP_CONTEXTUAL_FILTER = "CRF";
	public static final String EXP_CONTEXTUAL_FILTER_MYPARTNER_VALUE = "F";
	public static final String EXP_CONTEXTUAL_FILTER_DISABLE_VALUE = "A";
	public static final String EXP_CONTEXTUAL_FILTER_ENABLE_VALUE = "B";

	public static final String PAY_AT_HOTEL = "PAY_AT_HOTEL";
	public static final String CTA_RATES = "CTA_RATES";

	public static final String AE = "AE";
	public static final String KSA = "SA";
	public static final String AED = "AED";
    public static final String FITS = "Fits";
    public static final String GUEST_PROFILE = "GuestProfile";
    public static final String SAFETY_AND_HYGIENE = "SafetyandHygiene";
	public static final String HASH_SEPARATOR = "#";
	public static final String INCLUSIONS_DEFAULT_DOT_ICON_URL ="https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png";

	public static final String LOCALITY_GROUP = "LOCALITY";

	public static final String URL_PARAM_BASE_SPLITTER = "\\?";
	public static final String CHECK_AVAILBILITY_PARAM = "checkAvailability";

	public static final String REQUEST_SOURCE_KEY = "request-source";
	public static final String REQUEST_SOURCE_SCION = "SCION";
	public static final String AMP_SOURCE_CLIENT_SCION = "&srcClient=SCION";

	public static final String AMP_LENGTH_OF_STAY ="&los=";
	public static final String AMP_ADVANCE_PURCHASE ="&ap=";
	public static final String AMP_ADULT_COUNT ="&adult=";
	public static final String AMP_CHILD_COUNT ="&child=";
	public static final String AMP_PAGINATED ="&paginated=";
	public static final String OCCASSION_PACKAGE_ANGLE = "210";
	public static final String DIAGONAL_BOTTOM = "diagonalBottom";


	// spaceType Constants
	public static final String BEDROOM = "bedroom";
	public static final String LIVING_ROOM = "living_room";

	// homestays filterGroup constant from frontend
	public static final String VILLA_AND_APPT = "VILLA_AND_APPT";
	// homestays filterGroup constant from hotstore
	public static final String ALT_ACCO_PROPERTY = "ALT_ACCO_PROPERTY";
	// homestays filterValue constant from hotstore
	public static final String AltAcco = "AltAcco";

	public static final String BREAK_AMENITIES_BOLD ="<br> <b>AMENITIES:</b> ";

	public static final Integer NINE = 9;

	public static final Integer THREE = 3;

	public static final Integer SIX = 6;

	public static final String PRICE_RATIO = "PRICE_RATIO";
	public static final String VIEW_TYPE_GRAPH = "graph";
	public static final String PRICE_CATEGORY_NAME = "PRICE";
	public static final String PRICE_PER_NIGHT = "PRICE_PER_NIGHT";
	public static final String PRICE_PER_ROOM_PER_NIGHT = "PRICE_PER_ROOM_PER_NIGHT";


	public static final String MYPARTNER_EXPEDIA_PKG_RATE = "MYPARTNER_EXPEDIA_PKG_RATE";
	public static final String PACKAGE_RATE_KEY = "packageRateMapping";
	public static final String  TC_CLAUSE_KEY = "t&cMapping";
	public static final String PAY_LATER_CARD_EXP="payLaterCard";

	public static final String  AM = "AM";
	public static final String  PM = "PM";

	public static final String COUPLE_FRIENDLY_ID = "COUPLE_FRIENDLY";
	public static final String PIPE_SEPARATOR = " | ";
	public static final String LOCAL_ID_ACCEPTED = "LOCAL_ID_ACCEPTED";
	public static final String  DAYUSE_PARENTHESES_OPEN = " (";
	public static final String  DAYUSE_PARENTHESES_CLOSE = ")";
	public static final String  DAYUSE_SPACE = " ";
	public static final String  IMAGE_TEXT_H = "IMAGE_TEXT_H";
	public static final String  DAYUSE_LOCAL_ID = "DAYUSE_LOCAL_ID";

	public static final String CANCELLED = "cancelled";
	public static final String RECALLED = "recalled";
	public static final String ROOM_INFO_SUBTITLE = "ROOM_INFO_SUBTITLE";

	public static final String PROFESSIONAL = "professional";
	public static final String TRAVELLER = "traveller";
	public static final String  NULL_STRING = "null";
	public static final String GCC_EXCLUSIVE = "GCC_EXCLUSIVE";
	public static final String GCC ="GCC";
	public static final String MMT_EXCLUSIVE_IMAGE_URL = "https://promos.makemytrip.com/gcc/Badge_MMTExclusive_DT.png";
	public static final String MMT_EXCLUSIVE_TYPE = "EXCLUSIVE";
	public static final String BEST_PRICE_GUARANTEE = "BEST PRICE GUARANTEE";
	public static final String PENDING_AMOUNT = "Pending Amount";
	public static final String AMOUNT_PAID = "Amount Paid";

	public static final String GROUP_DEALS = "Group Deals";
	public static final String GROUP_BOOKING_TEXT = "groupBooking";
	public static final String NO_MEAL_INCLUSION_REMOVE = "noMealInclusionRemove";
	public static final String PEE = "PEE"; // PEE - Pricing Engine Enabled
	public static final String PEED = "PEED"; // PEED - Pricing Engine Enabled for Details
	public static final String newFilterService = "newFilterService"; // New Filter Service enabled
	public static final String newFilterServiceIH = "newFilterServiceIH"; // New Filter Service enabled
	public static final String OPTIMIZE_HOSTEL_SELECTION_EXP = "OHS";
	public static final String PROPERTY_CONFIG_CALLOUT_ENABLED = "PCCE";
	public static final String STAY_DETAIL_HOST_INFO_ENABLED = "staydetailshostinfo";
	public static final String ENABLE_MERGED_PROPERTY_TYPE = "enableMergedPropertyType";
	public static final String COUNTRY_PAGE_FILTER = "countryPageFilter";

	public static final String CITY_CODE_MALDIVES = "CTMALDI";
	public static final String TRANSFERS = "Transfers";
	public static final String CITY_TAX = "City Tax";
	public static final String GALA_MEALS = "Gala Meals";
	public static final String TRANSFERS_FEE_TEXT_KEY = "TFT";
	public static final String PROPERTY_TYPE = "PROPERTY_TYPE";
	public static final String MERGE_PROPERTY_TYPE = "MERGE_PROPERTY_TYPE";
	public static final String ALTACCO = "AltAcco";
	public static final String BOOK_NOW_AT_0 = "BOOK_NOW_AT_0";
	public static final String BOOK_NOW_AT_1 = "BOOK_NOW_AT_1";

	public static final String EXP_BNPL_NEW_VARIANT = "bnplNewVariant";
	public static final String EXP_BNPL_ZERO_VARIANT = "bnplZeroVariant";

	public static final String EXP_BNPL_PEAK_DATE = "isPeakDate";

	public static final String EXP_BNPL_PEAK_DAYS = "bnplPeakDays";

	public static final String EXP_BNPL = "BNPL";

	public static final String BOOK_NOW_MODAL_DATA_CONFIG = "bookNowModalData";
	public static final String BOOK_NOW_MODAL_HEADING = "heading";
	public static final String BOOK_NOW_MODAL_SUB_HEADING = "subHeading";
	public static final String BOOK_NOW_MODAL_DESCRIPTION = "description";
	public static final String BOOK_NOW_MODAL_BENEFITS = "benefits";
	public static final String BOOK_NOW_MODAL_OKAY_GOT_IT = "cta";
	public static final String BOOK_NOW_TEXT_KEY = "text";
	public static final String BOOK_NOW_PERSUASION_KEY = "bookNowTag";
	public static final String GST_ASSURED_PERSUASION_KEY = "gstAssuredTag";
	public static final String USER_ROLE_CONSTANT = "userRole";
	public static final String APPROVER = "APPROVER";
	public static final String APPROVAL_STATUS = "approvalStatus";
	public static final String CORP_APPROVER_PENDING_TITLE_KEY_EXPIRED = "CORP_APPROVER_PENDING_TITLE_KEY_EXPIRED";
	public static final String CORP_APPROVER_PENDING_SUBTITLE_KEY_EXPIRED = "CORP_APPROVER_PENDING_SUBTITLE_KEY_EXPIRED";
	public static final String CORP_REQUESTER_PENDING_TITLE_KEY_EXPIRED = "CORP_REQUESTER_PENDING_TITLE_KEY_EXPIRED";
	public static final String CORP_REQUESTER_PENDING_SUBTITLE_KEY_EXPIRED = "CORP_REQUESTER_PENDING_SUBTITLE_KEY_EXPIRED";
	public static final String CORP_REQUESTER_REJECTED_TITLE_KEY_EXPIRED = "CORP_REQUESTER_REJECTED_TITLE_KEY_EXPIRED";
	public static final String CORP_REQUESTER_REJECTED_SUBTITLE_KEY_EXPIRED = "CORP_REQUESTER_REJECTED_SUBTITLE_KEY_EXPIRED";
	public static final String FORWARD_BOOKING_DEEPLINK = "deepLink";
	public static final String FORWARD_BOOKING_CTA = "ctaText";
	public static final String ICON_URL_KEY = "iconUrl";

	// calendarAvailability constant
	public static final String AVAILABLE = "AVAILABLE";
	public static final String NOT_AVAILABLE = "NOT_AVAILABLE";
	public static final String MMT_VALUESTAYS_SUBTYPE = "VALUESTAYS";


	public static final String  CUMULATIVE_RATING_TEXT= "cumulativeRating";

	public static final String MP_FARE_HOLD = "MP_FARE_HOLD";
	public static final String LOG_HOVER_KEY = "logHover";
	public static final String LOG_HOVER_VALUE = "myp_hold_card_hover";
	public static final String TITLE_SUBTITLE_TOOLTIP = "TITLE_SUBTITLE_TOOLTIP";
	public static final String GET_APPROVAL_ENDPOINT = "cg/get-approvals";

	public static final String METRO = "Metro";
	public static final String BUS = "Bus";
	public static final String METRO_TAG = "METRO_STATION";
	public static final String BUS_TAG = "BUS_STATION";
	public static final String BUS_TERMINAL_TAG = "BUS_TERMINAL";
	public static final String METRO_TEXT = "Metro Access";
	public static final String BUS_TEXT = "Bus Access";

	public static final String LOCATION_PERSUASION_ID = "LOC_PERSUASION_";
	public static final String LOCATION = "LOCATION";
	public static final String STYLE_INLINE_BLOCK = "inlineBlock";
	public static final String NEW_HOTEL_LIST ="NHL";

	public static final String STYLE_DARK_TEXT = "darkText";
	public static final String STYLE_LOCATION = "pc__location";
	public static final String STYLE_BLUE_TEXT = "blueText";
	public static final String CORP_BUDGET_PROPERTIES_TEXT="CORP_BUDGET_PROPERTIES_TEXT";
	public static final String SIMILAR_HOTELS = "SIMILAR_HOTELS";
	public static final String SIMILAR_HOTELS_DT = "SIMILAR_HOTELS_DT";
	public static final String PERSONALISED_PICKS_HOTELS = "PERSONALISED_PICKS_HOTELS";
	public static final String LISTING_MAP = "LISTING_MAP";

	public static final String FILTER_PILL_EXP = "HFC"; //Experiment will be sent by client for this change HTL-38235
	public static final String ALERT_TYPE_VARIANT = "alert_type_variant"; //True for new App Versions configured by Pokus
	public static final String HTL_PILLS_ENABLED_EXP = "htlPillsEnabled";
	public static final String HTL_POPULAR_ENABLED_EXP = "htlPopularEnabled";

	public static final String EXP_CITY_COLLECTION_ENABLE = "cityCohortEnable";

	public static final String EXP_FALSE_0 = "0";
	public static final String EXP_TRUE_1 = "1";
	public static final String PRICER_V2 = "pricerV2";
	public static final String NBMF = "NBMF";
	public static final String HIDDEN_GEM = "Hidden Gem";
	public static final String HIDDEN_GEM_CARD = "HIDDENGEM";
	public static final String STAY_TYPE = "STAY_TYPE";
	public static final String STAY_TYPE_HOTEL = "HOTEL";
	public static final String STAY_TYPE_HOMESTAY = "HOMESTAY";

	public static final String TOGGLE_FILTERS_CATEGORY = "TOGGLE_FILTERS";
	public static final String MEAL_PREFERENCE_CATEGORY = "MEAL_PREFERENCES";

	public static final String EXTRA_BED_POLICY_TO_BE_REMOVED = "extrabedpolicy";
	public static final String GEC = "GEC";

	public static final String MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE = "myPartnerMoveToTdsTaxStructure";
	public static final String PACKAGE_RATE = "PACKAGE_RATE";

	public static final String INLINE = "INLINE";

	public static final String HOTELS_FILTER_CARD = "HOTELSFILTERCARD";

	/*Persuasions constants for Mypartner- Cashback offer & Hero Offer*/
	public static final String HERO_OFFER_PERSUASION_ICON_TYPE = "icHeroIcon";
	public static final String CASHBACK_OFFER_PERSUASION_ICON_TYPE = "icRewardBonus";
	public static final String CASHBACK_HERO_OFFER_PERSUASION_NODE = "cashbackHeroPersuasion";

	public static  final String CASHBACK_TO_WALLET ="Cashback to Wallet";

	public static final String PAGE_CONTEXT = "pageContext";

	public static final String UNIFIED_USER_RATING = "unifiedUserRating";
	public static final String NEW_PDT_LOGGING_ENABLED = "pdtLoggingEnable";
	public static final String CL_POKUS = "CL";
	public static final String EXTRA_BNB_EXP_KEY = "ihextrabnb";
	public static final String SHOW_RULES_EXP_KEY = "showWithPropertyRules";
	public static final String TOTAL_RATING_COUNT = "totalRatingCount";

	public static final String ADDITIONAL_INFORMATION = "Additional Information";
	public static final String ALT_ACCO_PROPERTIES = "Alt_Acco_properties";

	public static final String FoodAndDiningV2 = "FoodAndDiningV2";
	public static final String KITCHEN_CATEGORY_REVAMP = "Kitchen Information";
	public static final String MEALS_AND_COOK_DETAILS = "Meal Details";

	public static final String OCCUPANCY_PARAMETER = "{occupancy}";
	public static final String EXTRA_PARAMETER = "{extra}";

	public static final String BULLET_HTML = "\u2022";
	public static final String OPEN_BOLD_TAG = "<b>";
	public static final String CLOSE_BOLD_TAG = "</b>";

	public static final String FONT_COLOR_FC_SUB_TEXT = "<font color=\"#007E7D\">";
	public static final String GRADIENT_END_HOTEL_CLOUD = "#EBFEFB";
	public static final String CLOSE_FONT_TAG = "</font>";

	public static final String NIGHT_COUNT = "{NIGHT_COUNT}";
	public static final String ROOM_COUNT = "{ROOM_COUNT}";
	public static final String GRPN_GROUP_BOOKING_EXCLUSIVE_TAX_EXP = "GRPN";

	public static final String SAVING_PERC = "{PERCENTAGE}";

	public static final String TYPE_PILL = "pill";
	public static final String TYPE_FILTER = "filter";

	/*Peitho Persuasions constant for ShortStay Funnel*/
	public static final String SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_MAP = "PLACEHOLDER_CARD_M6";
	public static final String SHORTSTAYS_PERSUASION_PLACEHOLDER_ID_LISTING = "PLACEHOLDER_CARD_M4";
	public static final String SHORTSTAYS_PERSUASION_TEMPLATE_TYPE = "DEFAULT";
	public static final String SHORTSTAYS_PERSUASION_TYPE = "PEITHO";
	public static final String SHORTSTAYS_PERSUASION_ID_MAP = "SHORTSTAYS_PEITHO_M6";
	public static final String SHORTSTAYS_PERSUASION_ID_LISTING = "SHORTSTAYS_PEITHO_M4";
	public static final String SHORTSTAYS_STYLE_TEXT_COLOR_MAP = "#8B572A";
	public static final String SHORTSTAYS_STYLE_TEXT_COLOR_LISTING = "#FAD6A5";
	public static final String SHORTSTAYS_STYLE_FONT = "SMALL";
	public static final String MAP_DETAILS_EMPTY_MSG = "listingMapRequest.mapDetails must not be null";
	public static final String DRIVING_DURATION_HR = "DRIVING_DURATION_HR";
	public static final String IH_PROPERTY_FILTER_PILL = "IH_PROPERTY_FILTER_PILLS";
	public static final String IH_PROPERTY_FILTER_PILL_ID = "IHP";
	public static final String RUSH_DEAL_FILTER_PILL_ID = "RD";
	public static final String FREE_KID_STAY_FILTER_PILL_ID = "FKS";
	public static final String LAST_MIN_DEAL_FILTER_PILL_ID = "LMD";
	public static final String EARLY_BIRD_DEAL_FILTER_PILL_ID = "EBD";
	public static final String ALL_FILTERS_FILTER_PILL_ID = "FILTERS";
	public static final String SMART_FILTERS_PILL_ID = "SMART_FILTERS";


	public static final String RTB_EMAIL = "RTB_EMAIL";

	public static final String INSTANT_BOOKING = "INSTANT_BOOKING";

	public static final String DELAYED_CONFIRMATION = "DELAYED_CONFIRMATION";

	public static final String SPECIAL_FARE_TAG_SMALL = "SPECIAL_FARE_TAG_SMALL";

	public static final String SPECIAL_FARE_TAG_LARGE = "SPECIAL_FARE_TAG_LARGE";

	public static final String BOOKING_CONFIRMATION_TEXT = "BOOKING_CONFIRMATION_TEXT";

	public static final String SPECIAL_FARE_TAG_LARGE_STYLE = "specialFareTagLarge";

	public static final String SPECIAL_FARE_TAG_SMALL_STYLE = "specialFareTagSmall";

	public static final String SPECIAL_FARE_TAG_TOP = "specialFareTagTop";

	public static final String PRICE_BOTTOM_PLACEHOLDER_ID = "priceBottom";
	public static final String BNPL_DETAIL_PERSUASION_TITLE = "BNPL_DETAIL_PERSUASION_TITLE";

	public static final String BOTTOM_BOX_PLACEHOLDER_ID = "bottomBox";

	public static final String OVAL_PERSUASION_TEMPLATE = "OVAL";

	public static final String TEXT_IMG_PERSUASION_TEMPLATE = "TEXT_IMG";

	public static final String NO_OF_HOURS_PLACEHOLDER = "{NO_OF_HOURS}";

	public static final String PC_RIGHT_1_1_PLACEHOLDER = "PC_RIGHT_1_1";

	public static final String TEXT_IMAGE_H_TEMPLATE = "TEXT_IMAGE_H";

	public static final String PC_RIGHT_3_PLACEHOLDER = "PC_RIGHT_3";
	public static final String PC_IMG_ANNOTATION_PLACEHOLDER = "PC_IMG_ANNOTATION";

	public static final String PLACEHOLDER_PRICE_BOTTOM_M = "PLACEHOLDER_PRICE_BOTTOM_M";

	public static final String DISCOUNTS = "DISCOUNTS";

    public static final String BOTTOMSHEET = "BOTTOMSHEET";

    public static final String DEAL_BOX_IMAGE_TEXT_TEMPLATE = "DEAL_BOX_IMAGE_TEXT";

    public static final String CLIENT_APPS = "APPS";

    public static final String CANCEL_TYPE = "CANCEL_TYPE";

	public static final String MULTI_PERSUASION_V_TEMPLATE = "MULTI_PERSUASION_V";
	public static final String ENTIRE_PROPERTY_FILTER_NAME = "Entire Property";
	public static final String CONTEXTUALISATION_ENABLE_EXP = "contextEnable";
	public static final String CHEAPEST_BEST_REVIEWED_ENABLE_EXP = "sortCheapestBestReviewed";
	public static final String MOST_AND_BEST_REVIEWED_SORTING_ENABLE_EXP = "sortMostBestReviewed";
	public static final String AUTO_PAY = "AUTOPAY";
	public static final String FILTER_SCREEN_V4 = "FSV4";

	public static final String SPECIAL = "Special";

    public static final String FLYER_TEXT = "flyer";
	public static final String SALE_CAMPAIGN = "saleCampaign";

	public static final String BUS_DEAL_TEXT = "bus";
	public static final String TRAIN_TEXT = "train";

	public static final String CURRENCY_SYMBOL = "{currency_symbol}";
	public static final String AMOUNT = "{amount}";

	public static final String DEAL = "deal";
	public static final String DISCOUNT = "discount";
	public static final String EXPIRY = "expiry";
	public static final String TIMER = "timer";
	public static final String EARLY_BIRD = "EARLY_BIRD";
	public static final String LAST_MINUTE = "LAST_MINUTE";
	public static final String EARLY_BIRD_FILTER_TITLE = "EARLY_BIRD_FILTER_TITLE";
	public static final String LAST_MINUTE_FILTER_TITLE = "LAST_MINUTE_FILTER_TITLE";
	public static final String LOVED_BY_INDIANS_TITLE = "Loved by Indians";
	public static final String FREE_STAY_FOR_KIDS_FILTER_VALUE = "FREE_STAY_FOR_KIDS_AVAIL";
	public static final String WALLET_SURGE_PERSUASION_KEY ="WALLET_SURGE_PERSUASION_TEXT";
	public static final String BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED = "bnplDisabledDueToNonBnplCouponApplied";

	public static final String NON_BNPL_COUPON_APPLIED_CODE = "nonBnplCouponAppliedCode";

	public static final String SHOW_DISABLED_BNPL_DETAILS = "showDisabledBnplDetails";

	public static final String X_PERCENT_SELL_ON = "xPercentSellOn";
	public static final String X_PERCENT_SELL_ON_TEXT = "def_selling_true";
	public static final String X_PERCENT_SELL_OFF_TEXT = "def_selling_false";

	public static final String ACTIVE_BOOKINGS_THRESHOLD_REACHED = "activeBookingThresholdReached";

	public static final String NON_BNPL_COUPON_APPLIED = "nonBnplCouponApplied";
	public static final String _FC = "_FC";
	public static final String _NR = "_NR";
	public static final String COMBO_TITLE= "COMBO_TITLE_";
	public static final String ROOMS_COMBO ="ROOMS_COMBO";

	public static final String BRAND_MMT = "MMT";

	public static final String BRAND_EXP = "EXP";
	public static final String TIMEZONE = "GMT";
	public static final String INTEL_HOTEL = "IH";
	public static final String DOM_HOTEL = "DH";
	public static final String CONSUMER_NAME_HOTEL_ORCH = "HOTEL_ORCH";
	public static final String CONSUMER_NAME_HEADER = "CONSUMER_NAME";
	public static final String OPENING_CURLY_BRACE = "{";
	public static final String SINGLE_ = "SINGLE_";
	public static final String PLURAL_ = "PLURAL_";

	public static final String IMAGE_URL_ROOM_SIZE = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/size.png";
	public static final String IMAGE_URL_ROOM_NAME = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/view.png";
	public static final String IMAGE_URL_ROOM_TYPE = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/bed.png";

	public static final String IMAGE_URL_BATHROOM_TYPE = "https://promos.makemytrip.com/hotelfacilities/bathroom.png";
	public static final String SINGLE_BEDROOM_COUNT = "1";
	public static final String IMAGE_URL_DOUBLE_BED = "https://promos.makemytrip.com/images/CDN_upload/Full%20Bed.png";
	public static final String IMAGE_URL_SOFA_BED = "https://promos.makemytrip.com/images/CDN_upload/Sofa%20Bed.png";
	public static final String SINGLE_SOFA_BED = "1 sofa bed";
	public static final String COLON = ":";
	public static final String TRAFFIC_SOURCE = "trafficSource";
	public static final String TRANSIT_POIS = "TRANSIT_POIS";
	public static final String AND_STRING = "and";
	public static final String DATA = "data";
	public static final String STYLE_CLASSES = "styleClasses";
	public static final String PC__PEITHO = "pc__peitho";
	public static final String PC__CASHBACKDEAL = "pc__cashbackDeal";
	public static final String ICON_TYPE = "icontype";
	public static final String EXP_PERNEW = "PERNEW";
	public static final String EXP_MYPARTNER_LISTING_HN = "myPListingNew";
	public static final String EXP_MYPARTNER_HOTSTORE_FILTERS = "mmt.backend.hotel.default.listing.default.myp_hotstore_filters";
	public static final String EXP_ORCHESTRATOR_V2 = "mmt.backend.hotel.default.listing.default.orchestratorV2";

	public static final String DOMESTIC = "DOM";
	public static final String INTERNATIONAL = "INTL";
	public static final String E = "e";
	public static final String OFFER_DETAILS_API = "cg/getOfferDetails";
	public static final String PRICE_FOOTER_SUBTEXT = "({0} Night{1}/{2} Room{3})";
	public static final String PLURAL_STRING = "s";
	public static final String TITLE = "title";
	public static final String COLOR = "color";
	public static final String BG_URL = "bgUrl";
	public static final String HORIZONTAL_MARGIN = "horizontal";
	public static final String VERTICAL_MARGIN = "vertical";
	public static final String PERSUASION_BG_URL = "persuasionBgUrl";
	public static final String PERSUASION_HEADING_KEY = "headingText";
	public static final String POPULAR_PACKAGE = "POPULAR_PACKAGE";
	public static final String PLACEHOLDER_PC_BELOW_HOTEL = "PC_BELOW_HOTEL";
	public static final String THEMIFICATION_ENABLED = "reviewThemeV2";
	public static final String BLACK_REVAMP = "blackRevamp";
	public static final String MMT_SELECT_BRONZE = "BRONZE";
	public static final String ROOM_SCARCITY_CALLOUT = "roomScarcityCallOut";
	public static final String SHOW_REVIEW_NR_INCLUSION = "showReviewNRTimeline";
	public static final String ANY_OTHER_SPECIAL_REQUEST_CODE = "109";
	public static final String BEDS_SELLABLE_TYPE_FILTER_CODE = "DORMITORY_BEDS";
	public static final String ROOMS_SELLABLE_TYPE_FILTER_CODE = "PRIVATE_ROOMS";
	public static final String NEW_TCS_FLOW_POKUS = "newTcsFlow";
	public static final String EXP_RTBC ="RTBC";
	public static final String EXP_SPKG = "SPKG";
	public static final String RTB_SUB_TITLE_TEXT="RTB_SUB_TITLE_TEXT";
	public static final String RTB_TITLE_TEXT="RTB_TITLE_TEXT";
	public static final String RTB_BNPL_TEXT="RTB_BNPL_TEXT";
	public static final String UNDER_TEXT = "Under {0} {1}";
	public static final String ABOVE_TEXT = "{0} {1} & above";

	public static final String DESKTOP_CORP_INTL = "DESKTOP_CORP_INTL";
	public static final String ANDROID_CORP_INTL = "ANDROID_CORP_INTL";
	public static final String IOS_CORP_INTL = "IOS_CORP_INTL";
	public static final String PWA_CORP_INTL = "PWA_CORP_INTL";
	public static final String DESKTOP_CORP_DOM = "DESKTOP_CORP_DOM";
	public static final String ANDROID_CORP_DOM = "ANDROID_CORP_DOM";
	public static final String IOS_CORP_DOM = "IOS_CORP_DOM";
	public static final String PWA_CORP_DOM = "PWA_CORP_DOM";
	public static final String SEO_INTL = "SEO_INTL";
	public static final String SEO_DOM = "SEO_DOM";
	public static final String META_INTL = "META_INTL";
	public static final String META_DOM = "META_DOM";
	public static final String SEM_INTL = "SEM_INTL";
	public static final String SEM_DOM = "SEM_DOM";
	public static final String DESKTOP_GCC = "DESKTOP_GCC";
	public static final String ANDROID_GCC = "ANDROID_GCC";
	public static final String IOS_GCC = "IOS_GCC";
	public static final String PWA_GCC = "PWA_GCC";
	public static final String DESKTOP_HOTELS_INTL = "DESKTOP_HOTELS_INTL";
	public static final String ANDROID_HOTELS_INTL = "ANDROID_HOTELS_INTL";
	public static final String IOS_HOTELS_INTL = "IOS_HOTELS_INTL";
	public static final String PWA_HOTELS_INTL = "PWA_HOTELS_INTL";
	public static final String DESKTOP_HOTELS_DOM = "DESKTOP_HOTELS_DOM";
	public static final String ANDROID_HOTELS_DOM = "ANDROID_HOTELS_DOM";
	public static final String IOS_HOTELS_DOM = "IOS_HOTELS_DOM";
	public static final String PWA_HOTELS_DOM = "PWA_HOTELS_DOM";
	public static final String DESKTOP_HOMESTAY_INTL = "DESKTOP_HOMESTAY_INTL";
	public static final String ANDROID_HOMESTAY_INTL = "ANDROID_HOMESTAY_INTL";
	public static final String IOS_HOMESTAY_INTL = "IOS_HOMESTAY_INTL";
	public static final String PWA_HOMESTAY_INTL = "PWA_HOMESTAY_INTL";
	public static final String DESKTOP_HOMESTAY_DOM = "DESKTOP_HOMESTAY_DOM";
	public static final String ANDROID_HOMESTAY_DOM = "ANDROID_HOMESTAY_DOM";
	public static final String IOS_HOMESTAY_DOM = "IOS_HOMESTAY_DOM";
	public static final String PWA_HOMESTAY_DOM = "PWA_HOMESTAY_DOM";
	public static final String PRIVATE_ROOMS_AVAILABLE = "Private rooms also available";
	public static final String PRIVATE_TAG = "Private";
	public static final String SHARED_TAG = "Shared";

	public static final String BATHROOMS="Bathrooms";
	public static final String BEDROOM_1 = "Bedroom 1";
	public static final String BATHROOM="Bathroom";
	public static final String HERO_POI_DRIVING_DISTANCE_TITLE = "HERO_POI_DRIVING_DISTANCE_TITLE";
	public static final String HERO_POI_DRIVING_DISTANCE_TEXT = "HERO_POI_DRIVING_DISTANCE_TEXT";
	public static final String IST = "IST";
	public static final String GULF_STANDARD_TIME = "Gulf Standard Time";
	public static final String PRIVILEGED_USER = "PRIVILEGED_USER";
	public static final String CURSED_USER = "CURSED_USER";
	public static final String UNFORTUNATE_USER = "UNFORTUNATE_USER";

	public static final String HOTEL_FUNDED_DISCOUNT = "HOTEL_FUNDED_DISCOUNT";
	public static final String PLACEHOLDER_PC_HOTEL_TOP = "PC_HOTEL_TOP";
	public static final String PLACEHOLDER_AVAILABLE_COUNT = "{available_count}";
	public static final String PLACEHOLDER_PLURAL = "{s}";
	public static final String STARTS_AT = "Starts at";
	public static final String SQUARE_FEET = "sq ft";
	public static final String HINDI_RUPEE = "₹";

	public static final String REVIEW_PRICE_REQUEST = "reviewPriceRequest";
	public static final String EMI_DETAIL_REQUEST = "emiDetailRequest";

	public static final String FILTER_VALUE_SPLITTER_DPT = "\\^";

	public static final String FILTER_GROUP_SPLITTER_DPT = "#";

	public static final String FILTER_GROUP_VALUE_SPLITTER_DPT = "=";


	public static final String MATCHMAKER_AREA = "area";

	public static final String MATCHMAKER_POI = "poi";

	public static final String SELECTED_AREA_TEXT = "selected area";

	public static final String SELECTED_POI_TEXT = "selected poi";
	public static final String GOOGLEHOTELDFINDER = "GOOGLEHOTELDFINDER";

	public static final String EXACT_ROOM_RECOMMENDATION= "EXACT_ROOM_RECOMMENDATION";
	public static final String CHAT_GPT_SUMMARY_EXP = "chatGptSummary";
	public static final String CODE_AltAco = "AA";
	public static final String INTEL_META = "intlMetaV2";
	public static final String DOM_META = "domMetaV2";
	public static final String ALT_META = "altAccoMetaV2";
	public static final String GCC_META = "gccMetaV2";
	public static final String OS = "os";
	public static final String ORG = "org";
	public static final String ORG_ID = "orgID";
	public static final String EMPLOYEE = "employee";
	public static final String USER_AGENT = "user-agent";
	public static final String RTB_CHECKBOX_TEXT="RTB_CHECKBOX_TEXT";
	public static final String RTB_CHECKBOX_ERROR_TEXT="RTB_CHECKBOX_ERROR_TEXT";
	public static final String RTB_DT_SUBTITLE_WITH_SEPARATOR = "RTB_DT_SUBTITLE_WITH_SEPARATOR";

	public static final String VIEW_ALL_PACKAGES = "View All Packages";
	public static final String FFFFFF = "#FFFFFF";
	public static final String ffeaa7 = "#ffeaa7";
	public static final String FAF2E4 = "#FAF2E4";
	public static final String D3E7FF = "#D3E7FF";
	public static final String D0021B = "#d0021b";
	public static final String CF8100 = "#CF8100";
	public static final String DIAGONAL_START = "diagonalStart";

	public static final String SHOW_ALL_PACKAGES = "SHOW ALL SUPER PACKAGES";
	public static final String SUPER_PACKAGE_CARD_TEXT = "SUPER_PACKAGE_CARD_TEXT_V2";
	public static final String SUPER_PACKAGES_TEXT = "SUPER_PACKAGES_TEXT";
	public static final String PACKAGE_RATE_TRACKING_TEXT = "SuperPackage_Booked";
	public static final String MEAL_UPSELL_TRACKING_TEXT = "mealplan_upsell_booked";

	public static final String B2C_BUSINESS_HOTELS_TRACKING_TEXT = "b2c_business_hotels";
	public static final String EXP_GALLERY_V2 = "GALLERYV2";
	public static final String PIPE_SEPARATOR_WITH_BACKSLASH = " \\| ";
	public static final String EXTRA = "Extra";
	public static final String ADULT = "Adult";
	public static final String PLURAL_ADULTS = "Adults";
	public static final String CHILD = "Child";
	public static final String CHILDREN = "Children";
	public static final String EXP_PLV2 = "PLV2";
	public static final String BEDROOM_COUNT = "BEDROOM_COUNT";
	public static final String EXP_EBCF = "EBCF"; //Enable Bedroom Count Filter experiment Key
    public static final String SEARCH_ROOMS_END_POINT = "cg/search-rooms/";
    public static final String DT_INCLUSION_HTML = "DT_INCLUSION_HTML";
    public static final String APPS_INCLUSION_HTML = "APPS_INCLUSION_HTML";
    public static final String CARD_TYPE_COMPACT = "COMPACT";
    public static final String PLUS = "+";
	public static final String EARLY_MORNING_TIME = "earlyMorningTime";
	public static final String DAY_TIME = "dayTime";
	public static final String EARLY_NIGHT_TIME = "earlyNightTime";
	public static final String LATE_NIGHT_TIME = "lateNightTime";
	public static final String DASH_SEPARATOR = " - ";
	public static final String checkInAddition = "checkInAddition";
	public static final String checkOutAddition = "checkOutAddition";
	public static final String defaultRooms = "defaultRooms";
	public static final String defaultGuest = "defaultGuest";
	public static final String SKYSCANNER = "SKYSCANNER";
	public static final String GET_RATE_V2 = "getRateV2";
	public static final String EP_MEAL_PLAN = "EP";
	public static final String MEAL_UPSELL_CATEGORY = "MEAL_UPSELL";
	public static final String DDMMYYYY ="ddMMyyyy";
	public static final String GRADIENT_END = "#CCE8FF";
	public static final String GRADIENT_END_V2 = "#CEF1EB";

	public static final String GRADIENT_END_FOREX = "#E9D4FF";

	public static final String SUB_GRADIENT_START = "#C86DD7";
	public static final String SUB_GRADIENT_END = "#3023AE";
	public static final String maxCouponCountExp = "mmt.backend.hotel.default.review.default.MaxCouponCount";
	public static final String ihCashbackSectionExp = "mmt.backend.hotel.default.review.default.IHCashbackSectionEnable";
	public static final String callToBook = "mmt.backend.hotel.default.listing.default.callToBook";
	public static final String DIRECTION_DIAGONAL = "diagonal_bottom";
	public static final String DIRECTION_VERTICAL = "vertical";
	public static final String APPLY = "Apply";
	public static final String CANCEL = "Cancel";

	public static final String CONTINUE = "Continue";
	public static final String COUPON_TYPE_BENEFIT = "BENEFIT_DEAL";
	public static final String CASHBACK = "Cashback";
	public static final String OFF_TEXT = "Off";
	public static final String OFFER_AND_TERMS = "Offer T&C";
	public static final String BUY_FOREX_NOW = "BUY FOREX NOW";
	public static final String BOOK_CAB_NOW = "BOOK CAB NOW";
	public static final String FOREX_ANCILLARY_NAME_FLIGHTS = "mmt_ih_forex_cashback";
	public static final String FOREX_ANCILLARY_NAME_CABS = "mmt_ih_cab_cashback";
    public static final String HOMESTAY_V2_FLOW = "HOMESTAY_V2_FLOW";
	public static final String SEASON_CARD_ID = "SEASON";
	public static final String PERSUASION_CARD_ID = "PERSUASION";
	public static final String RTB_DAY_NIGHT_EXP = "mmt.backend.hotel.default.default.default.RTBDayNight";
	public static final String CROSS_SELL_TITLE = "CROSS_SELL_TITLE";
	public static final String CROSS_SELL_SUBTITLE = "CROSS_SELL_SUBTITLE";
	public static final String CROSS_SELL_LOGO_URL_DT = "CROSS_SELL_LOGO_URL_DT";
	public static final String CROSS_SELL_LOGO_URL_APP = "CROSS_SELL_LOGO_URL_APP";
	public static final String CROSS_SELL_FLIGHT_REQUEST = "FLIGHTSTY";
	public static final String CROSS_SELL_OFFER_TEXT = "CROSS_SELL_OFFER_TEXT";
	public static final String CROSS_SELL_FOOTER_TEXT = "CROSS_SELL_FOOTER_TEXT";
	public static final String CROSS_SELL_FALLBACK_IMG_DT = "CROSS_SELL_FALLBACK_IMG_DT";
	public static final String CROSS_SELL_FALLBACK_IMG_APP = "CROSS_SELL_FALLBACK_IMG_APP";
	public static final String CROSS_SELL_OFFER_SUBTEXT = "CROSS_SELL_OFFER_SUBTEXT";
	public static final String CROSS_SELL_BUTTON_TEXT = "CROSS_SELL_BUTTON_TEXT";
	public static final String CROSS_SELL_COUPON_CODE = "CROSS_SELL_COUPON_CODE";
	public static final String BG_GRAD_COLOR_1 = "bgGradientColor1";
	public static final String BG_GRAD_COLOR_2 = "bgGradientColor2";
	public static final String BG_GRAD_COLOR_3 = "bgGradientColor3";
	public static final String BTN_BG_COLOR = "buttonBgColor";
	public static final String LOOP_COUNT = "loopCount";
	public static final String BORDER_COLOR_1 = "borderColor1";
	public static final String BORDER_COLOR_2 = "borderColor2";
	public static final String BORDER_COLOR_3 = "borderColor3";
	public static final String BORDER_COLOR_4 = "borderColor4";
	public static final String BORDER_COLOR_5 = "borderColor5";
	public static final String ANIMATION_DELAY = "animationDelay";
	public static final String CORNER_COLOR = "cornerColor";
	public static final String OVERLAY_COLOR = "overlayColor";
	public static final String FONT = "font";
	public static final String ICON = "icon";
	public static final String NULL_CITY = "this city";
	public static final String HOTEL_CROSS_SELL = "HOTEL_CROSS_SELL";
  	public static final String TREELS = "TREELS";
	public static final String NO_COST_EMI_APPLICABLE = "mmt.backend.hotel.default.listing.default.NCEA";
	public static final String MP_SUGGESTED_FILTER_ENABLED = "mmt.backend.hotel.default.listing.default.myp_suggested_filters";
  	public static final String ICV2 = "ICV2";
	public static final int LAKH = 100000;
	public static final int CRORE = 10000000;
	public static final String LAKH_PREFIX = "L";
	public static final String CRORE_PREFIX = "Cr";
	public static final String FALLBACK_BLACK_GRADIENT = "FALLBACK_BLACK_GRADIENT";

	public static final String CONSUL_NODE_ACTION_INFO = "actionInfo";

	public static final String SELLABLE_UNIT_ENTIRE = "entire";

	public static final String SELLABLE_UNIT_ROOM = "room";

	public static final String SELLABLE_UNIT_ENTIRE_CONSUL_KEY = "ENTIRE_PROPERTY";

	public static final String SAVED_FOR_COMPANY_PILL_ID = "SAVED_FOR_COMPANY";
	public static final String SELLABLE_UNIT_ROOM_CONSUL_KEY = "PRIVATE_ROOMS";

	public static final String HOMESTAY_V2_FILTERS_PILL_EXP = "mmt.backend.hotel.default.listing.default.homestayV2filtersPill";

	public static final String GROUP_FUNNEL_ENHANCEMENT_EXP = "mmt.backend.hotel.default.listing.default.groupFunnelEnhancement";

	public static final String BASE_FONT_SIZE = "BASE";

	public static final String COMMONS_LANDING_PAGECONTEXT = "APP_LANDING";

	public static final String COMMONS_THANKYOU_PAGECONTEXT = "THANKYOU";

	public static final String CROSS_SELL_TITLE_FT = "CROSS_SELL_TITLE_FT";
	public static final String CROSS_SELL_SUBTITLE_FT = "CROSS_SELL_SUBTITLE_FT";
	public static final String CROSS_SELL_OFFER_TEXT_FT = "CROSS_SELL_OFFER_TEXT_FT";
	public static final String CROSS_SELL_FOOTER_TEXT_FT = "CROSS_SELL_FOOTER_TEXT_FT";
	public static final String CROSS_SELL_OFFER_SUBTEXT_FT = "CROSS_SELL_OFFER_SUBTEXT_FT";
	public static final String CROSS_SELL_BUTTON_TEXT_FT = "CROSS_SELL_BUTTON_TEXT_FT";
	public static final String CROSS_SELL_COUPON_CODE_FT = "CROSS_SELL_COUPON_CODE_FT";

	public static final String CROSS_SELL_TITLE_HP = "CROSS_SELL_TITLE_HP";
	public static final String CROSS_SELL_SUBTITLE_HP = "CROSS_SELL_SUBTITLE_HP";
	public static final String CROSS_SELL_OFFER_TEXT_HP = "CROSS_SELL_OFFER_TEXT_HP";
	public static final String CROSS_SELL_FOOTER_TEXT_HP = "CROSS_SELL_FOOTER_TEXT_HP";
	public static final String CROSS_SELL_OFFER_SUBTEXT_HP = "CROSS_SELL_OFFER_SUBTEXT_HP";
	public static final String CROSS_SELL_BUTTON_TEXT_HP = "CROSS_SELL_BUTTON_TEXT_HP";
	public static final String CROSS_SELL_COUPON_CODE_HP = "CROSS_SELL_COUPON_CODE_HP";

	public static final String CROSS_SELL_TITLE_TP = "CROSS_SELL_TITLE_TP";
	public static final String CROSS_SELL_SUBTITLE_TP = "CROSS_SELL_SUBTITLE_TP";
	public static final String CROSS_SELL_OFFER_TEXT_TP = "CROSS_SELL_OFFER_TEXT_TP";
	public static final String CROSS_SELL_FOOTER_TEXT_TP = "CROSS_SELL_FOOTER_TEXT_TP";
	public static final String CROSS_SELL_OFFER_SUBTEXT_TP = "CROSS_SELL_OFFER_SUBTEXT_TP";
	public static final String CROSS_SELL_BUTTON_TEXT_TP = "CROSS_SELL_BUTTON_TEXT_TP";
	public static final String CROSS_SELL_COUPON_CODE_TP = "CROSS_SELL_COUPON_CODE_TP";

	public static final String VALUE_STAYS_TOOL_TIP_NEW_DETAIL_PAGE_DT = "VALUE_STAYS";


	public static final String PLACEHOLDER_CARD_M1_TEXT_COLOR = "#4A4A4A";

	public static final String HOTEL = "Hotel";
	public static final String RESORT = "Resort";

	public static final String OSBA = "OSBA";
	public static final String GBF = "GBF";
	public static final String MP_EXPRESS_CHECKOUT_EXP_KEY = "MypartnerExpressCheckout";
	public static final String MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY = "myPartnerMoveToTdsTaxStructure";
	public static final String MP_GST_SAVING_EXP_KEY ="gstSavingEnabled";
public static final String MP_CUSTOMER_GST_EXP_KEY ="mypartner_customer_GST";
	public static final String MP_BLOCK_USER_MANDATORY_EXP_KEY ="blockUserWhenMandatory";

	public static final String TOOL_TIP_TYPE = "tooltipType";

	public static final String DISCOUNT_AMT = "{DISCOUNT_AMT}";
	public static final String BOOKING_COUNT = "{BOOKING_COUNT}";

	public static final String MMT_EXCLUSIVE_IMAGE_URL_NEW_DT_PAGE = "https://promos.makemytrip.com/gcc/Badge_MMTExclusive_DT.png";

	public static final String EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING = "FIRST_FIVE_BOOKING";

	public static final String FIRST_FIVE_BOOKING_ICON_URL= "https://promos.makemytrip.com/Growth/Images/B2C/new_offer_icon_dh.png";

	public static final String FIRST_FIVE_BOOKING_BG_COLOR= "#E6FFF9";

	public static final String FIRST_FIVE_BOOKING_ICON_TYPE= "offerText"; // Only For Desktop

	public static final String EXP_GBRP = "GBRP";
	public static final String EXP_ADD_ON_PERSUASION = "addOnPersuasion";

	public static final String EXTENDED_USER = "extendedUser";

	public static final String WITH="with";

	public static final String ID_CONTEXT_BUSINESS = "1";

	public static final String ID_CONTEXT_PERSONAL = "0";

	public static final String REVIEW_RATING_TITLE = "Review & Rating";

	public static final String UGCV2 = "UGCV2";

	public static final String PROFILE_TYPE = "0";
	public static final String DATE = "date";
	public static final String DOM_LOB = "dom_htl";
	public static final String ENTIRE_APARTMENT = "Entire Apartment";
	public static final String ENTIRE_APARTMENTS = "Entire Apartments";
	public static final String ENTIRE_SERVICE_APARTMENT = "Entire Serviced Apartment";
	public static final String ENTIRE_SERVICE_APARTMENTS = "Entire Serviced Apartments";
	public static final String ROOM_IN_APARTMENT = "Room in an Apartment";
	public static final String ROOM_IN_SERVICE_APARTMENT = "Room in a Serviced Apartment";
	public static final String FLEXI_CANCEL = "FLEXI_CANCEL";
	public static final String PREMIUM = "PREMIUM";

	public static final String ROOM_VALUE = "ROOM";

	public static final String BRAND_FILTER = "BRAND_FILTER";

	public static final String PAY_ON_MMT_ONLY_INSTRUCTION = "PAY_ON_MMT_ONLY_INSTRUCTION";

	public static final String ATTRIBUTE_INDEPENDENT_PROPERTY = "independent_property";
	public static final String COMPACT_V2_HOTEL_CARD_TYPE = "compactV2";

	public static final String COMPACT_V1_HOTEL_CARD_TYPE = "compact";

	public static final  String DIRECT_HOTEL = "DIRECT_HOTEL";
	public static final String EXPERIMENT_KEY_VALUE_B = "B";
	public static final String EXPERIMENT_KEY_VALUE_C = "C";
	public static final String FILTER_WI_FI = "Wi-Fi";
	public static final String AMENITIES = "AMENITIES";
	public static final int HN_TEXT_LIMIT_AMENITIES = 43;
	public static final String IMAGES_EXP_ENABLE = "imageExperimentEnable";
	public static  final  String CONNECTIVITY = "2";
	public static final int HN_TEXT_DEC_RATE = 5;
	public static final String BUT_SEPARATOR = ", but ";

	public static final String REQUEST_IDENTIFIER = "requestId";

	public static final Set<String> API_ERROR_CODES = new HashSet<>(Arrays.asList("402405","400103"));

	public static final String ONE_ON_ONE_RATE_SEGMENT = "1135";
	public static final String HOTEL_CLOUD_RATE_SEGMENT = "1153";
	public static  final  String POPULAR_WITH_GUESTS = "Popular with Guests";
	public static final String PROPERTY_TYPE_APARTMENT = "apartment,apart-hotel";
	public static final String APARTMENT_SELLABLE_UNIT_ENTIRE_CONSUL_KEY = "ENTIRE_SERVICE_APARTMENT";
	public static final String APARTMENT_SELLABLE_UNIT_ROOM_CONSUL_KEY = "PRIVATE_SERVICE_APARTMENT";
	public static final String PROPERTY_STAY_TYPE_TITLE_ENTIRE = "What is an {0}?";
	public static final String PROPERTY_STAY_TYPE_TITLE_ROOM = "What is {0}?";
	public static final String PROPERTY_STAY_TYPE_TITLE_ENTIRE_V1 = "What is an Entire {0}?";
	public static final String PROPERTY_STAY_TYPE_TITLE_ROOM_V1 = "What is Room in {0}?";
	public static final String LOCATION_FILTER_GROUP = "LOCATION";
	public static final String FILTER_CATEGORY_TYPE = "filter_category";
	public static final String TRAVEL_TIPS_SUB_HEADER = "Travel Tips";
    public static final String ERROR_CODE_DATA_NOT_FOUND = "404";
	public static final String ERROR_MESSAGE_CITY_DATA_NOT_AVAILABLE = "Data not available for the requested city , Please try again later.";
	public static final String ERROR_MESSAGE_COULD_NOT_FETCH_UUID = "Could not fetch uuid";
	public static final String callToBookOption = "callToBook";
	public static final String detailDeepLinkUrl = "detailDeepLinkUrl";
	public static final String LOS = "LOS";
	public static final String LONGSTAY = "LONGSTAY";
	public static final String ListAllProp = "ListAllProp";
	public static final String HighValue = "HighValue";
	public static final String CallToBook = "CallToBook";
	public static final String FLYER_EXCLUSIVE_ICON_COLOR = "#FFFFFF";
	public static final String FLYER_EXCLUSIVE_ICON_BORDER_COLOR = "#0c58b4";
	public static final String CASHBACK_IDENTIFIER = "CASHBACK";
	public static final String CABCASHBACKCARD = "CABCASHBACKCARD";
	public static final String FOREXCASHBACKCARD = "FOREXCASHBACKCARD";
	public static final String Street_View = "Street View";
	public static final String SQUARE_FEET_V2 = "sq.ft";
	public static final String SQUARE_METER = "sq.mt";
	public static final double SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR = 0.092903;

	public static final String TITLE_TEXT = "TITLE";

	public static final String MULTI_ROOM_EXP = "roomCountDefault";
	public static final String AA_BEDROOM_INPUT_EXP= "aabedroominput";

	public static final String FLEXIBLE_ROOM_VALUE = "flexible";
	public static final String EXACT_ROOM_VALUE = "exactRoom";


	public static final String extraGuestFreeChildColor = "#FF0000";
	public static final String longStayGccNudgeIconUrl = "https://someurl.com/icon.png";
	public static final String PERSUASION_TEXT_TEMPLATE = "<font color=\"%s\">%s</font>";
	public static final String CHEAPER_BY = "Cheaper by";
	public static final String EXPENSIVE_BY = "Expensive by";
	public static final String SELECTED_PRICE = "Selected Price";
	public static final String CHEAPEST_PRICE = "Cheapest Price";

	public static final String NO_DATA_FOUND_FOR_UUID = "No data fetched from mojo api for uuid : ";
	public static final String GREEN_TAX = "green_tax";

	public static final String MYB_NEW_DETAILS_EXP_KEY = "myb_new_details";
	public static final String MYBIZ_ASSURED_NEW_TITLE = "This Property is";
	public static final String NON_MYBIZ_ASSURED_NEW_ICON_URL = "https://promos.makemytrip.com/mybiz/newdetailnonmba.png";
	public static final String MYBIZ_ASSURED_NEW_ICON_URL = "https://promos.makemytrip.com/mybiz/newdetailmba.png";
	public static final String DELAYED_CONFIRMATION_BG_COLOR = "#ffedd1";
	public static final String DELAYED_CONFIRMATION_MYB_NEW_DETAILS_BG_COLOR = "#fffaf1";
	public static final String MAX_CHILD_AGE = "MAX_CHILD_AGE";


	public static final String DEEPLINK_FILTER_DATA = "filterData";
	public static final String DEEPLINK_FILTER_GROUP_SPLITTER = "^";
	public static final String AND_SEPARATOR = "&";
	public static final String RSC = "rsc";
	public static final String MPN = "mpn";
	public static final String CHECK_IN_TIME = "checkInTime";

	public static final String LOCUSTYPE_URL_PARAM = "locusType";
	public static final String LOCUSID_URL_PARAM = "locusId";
	public static final String SEARCHTEXT_URL_PARAM = "searchText";
	public static final String STAYCATION_FILTER_URL_PARAM = "staycation";

	public static final String HIGH_BHF = "High_BHF";
	public static final String LOW_BHF = "Low_BHF";

	public static final String HOTEL_NAME = "hotelName";
	public static final String COUNTRY_NAME = "countryName";
	public static final String CITY_NAME = "cityName";
	public static final String ENTIRE_PROPERTY = "isEntireProperty";

	public static final String PR_SEPARATOR = "=";
	public static final String PIPE_SPLITTER = "|";
	public static final String FUNNEL_NAME = "funnelName";

	public static final String PARENT_LOCATION_ID = "parentLocId";
	public static final String PARENT_LOCATION_type = "parentLocType";
	public static final String REGION_URL_PARAM = "region";
	public static final String HOMESTAY_URL_PARAM = "homestay";
	public static final String HYPHEN = "-";
	public static final String MM_AREA_TAG = "mmAreaTag";
	public static final String MM_POI_TAG = "mmPoiTag";
	public static final String REQUESTOR_SCION = "SCION";
	public static final String TRAFFIC_SOURCE_CROSSSELL = "CROSSSELL";

	public static final String PIPE_UNICODE = "%7C";
	public static final String SPACE_UNICODE = "%20";


	public static final String ROOM_STAY_QUAL = "roomStayQualifier";
	public static final String AP = "AP";
	public static final String HOTEL_CATEGORY = "hotelCategory";


	public static final String CHECK_IN = "checkin";
	public static final String CHECK_OUT = "checkout";

	public static final String COMPONENT = "cmp";
	public static final String DEEPLINK_PRICE_BY_TEXT = "priceBy";
	public static final String APP_SHARE_HOT_DETAILS = "hotelAppShareNew";

	public static final String LOCUS_ID = "locusId";
	public static final String LOCUS_LOCATION_TYPE = "locusType";
	public static final String CITY = "city";
	public static final String WALLET_REGION_IND = "IN";
	public static final String VIEW_TYPE = "viewType";

	//Welcom Offer Coupon
	public static final String lpgWelcomeOffer = "lpgWelcomeOffer";
	public static final String WELCOMEMMT = "WELCOMEMMT";
	public static final String QR_CODE = "QRCode";
	public static final String FND_MERGE_ID_STRING = "MEALS";

	public static final String ALT_DATE_BOTTOMSHEET_LOGO_URL = "https://promos.makemytrip.com/GCC/MiscIcons/calendaricon.png";
	public static final String ALT_DATE_PERSUASION_PLACEHOLDER = "PLACEHOLDER_CARD_M2";
	public static final String SCION_PERSUASION_PLACEHOLDER = "PC_SCION_BLACK";
	public static final String m_c54_tracking_key = "m_c54";
	public static final String GLOBAL_ENTITY = "GLOBAL";
	public static final String UNITS_OF = "units of";

	public static final String LINKED_RATE_EXPERIMENT_NR = "showNRPlan";
	public static final String LINKED_RATE_PLAN_TYPE = "linkedRatePlan";

	public static final String YYYY_MM_DD = "yyyy-MM-dd";
	public static final String dd_MMM_YYYY = "dd MMM yyyy";
	public static final String MMT_AUTH = "mmtAuth";
	public static final String AUTH = "mmt-auth";

    public static final String FOREX_CAB_CARD_TEMPLATE_ID = "FAO_FOREX_CAB";
	public static final String FOREX_CAB_CARD_ID = "fao";
	public static final String FOREX_CARD_ID="forex";
	public static final String CAB_CARD_ID="cab";

	public static final String MYPRT = "MYPRT";
	public static final String Y_LOWER_CASE = "y";
	public static final String OPEN_PARENTHESIS = "(";
	public static final String CLOSE_PARENTHESIS = ")";
	public static final String EXTRA_ADULT_CHILD = "EXTRA_ADULT_CHILD";

	public static final String COMBINED_OTA = "combinedOTA";
	public static final Integer TRACKING_MAP_THRESHOLD = 5;
	public static final String EVAR_126 = "m_v126";
	public static final String LINKEDRATE_FCNR = "CANCELLATION_POLICY_NR";

	//mypartner location based
	public static final String SEARCH_ENABLED = "SEARCH_ENABLED";
	public static final String BOOKING_ENABLED = "BOOKING_ENABLED";
	public static final String BNPL_ENABLED = "BNPL_ENABLED";
	
	public static final String OFFERS_FILTER_CATEGORY = "OFFERS";
	public static final String EXP_OFFERS_UI_REDESIGN = "newOffersUi";
	public static final String ONLY_TODAY_DEAL_THANK_YOU_TRACKING_TEXT = "OTCthankYou";

	public static final String TRAVEL_PLEX_ENABLED = "TPE";
	public static final String COUPON_GREEN_STRIP = "COUPON_GREEN_STRIP";
	public static final String LANGUAGES_SPOKEN = "LANGUAGES_SPOKEN";
	public static final String EXTRA_BED_POLICY = "EXTRA_BED_POLICY";
	public static final String BREAKFAST_CHARGES = "BREAKFAST_CHARGES";

	//FoodDiningV2 Constants
	public static final String FEEDBACK_ITEM_TYPE_TEXT = "text";
	public static final String FEEDBACK_ITEM_TYPE_TEXTBOX = "textBox";
	public static final String MEAL_OPTION_TYPE_FIXED_MENU = "FIXED_MENU";
	public static final String MEAL_OPTION_TYPE_COOK = "COOK";
	public static final String MEAL_CONTEXT_PRICING = "MEAL_CONTEXT_PRICING";

	public static final String OPEN_FONT_TAG_00000 =  "<font color=\"#000000\">";
	public static final String OPEN_FONT_TAG_007E7D =  "<font color=\"#007E7D\">";
	public static final String OPEN_FONT_TAG_CF8100 =  "<font color=\"#CF8100\">";
	public static final String OPEN_FONT_TAG_EC2127 =  "<font color=\"#EC2127\">";
	public static final String REVIEW_COUNT = "{REVIEW_COUNT}";
	public static final String NATIONALITY = "{NATIONALITY}";
	public static final Map<String, String> NATIONALITY_MAP = new HashMap<String, String>() {
		{
			put("INDIA", "Indian");
		};
	};
	public static final int MIN_COUNTRY_WISE_REVIEW_COUNT = 2;
	public static final String COUNTRY_WISE_REVIEW_TEXT_COLOR = "#A5572A";
	public static final String TIER_NAME =  "{TIER_NAME}";
	public static final String CASHBACK_AMOUNT =  "{CASHBACK_AMOUNT}";
    public static final String F1FFFF = "#F1FFFF";
	public static final String FFF7EB = "#FFF7EB";
	public static final String EFF6FF = "#EFF6FF";
	public static final String EAF5FF = "#EAF5FF";

	// DigiLocker card constants
	public static final String DIGILOCKER_CARD_ID = "dgo";
	public static final String DIGILOCKER_BORDER_COLOR = "#D8D8D8";
	public static final String DIGILOCKER_BG_START_COLOR = "#FFFFFF";
	public static final String DIGILOCKER_BG_END_COLOR = "#D3E7FF";
	public static final String DIGILOCKER_BG_CENTER_COLOR = "#FFFFFF";
	public static final String DIGILOCKER_BG_DIRECTION = "diagonal_top";
	public static final String DIGILOCKER_BG_ANGLE = "45";
	public static final String DIGILOCKER_DEEPLINK_ACT_PARAM = "act";
	public static final String DIGILOCKER_DEEPLINK_ACT_VALUE = "133";

	public static final String NEXT_TIER_BANNER_FAREBREAKUP = "NEXT_TIER_BANNER_FAREBREAKUP";
	public static final String KEY_EXP_MOST_BOOKED_RANK = "mostbookedrank";
	public static final String COMBO_SAVING_KEY = "comboSavingTag";
	public static final String COMBO_SAVING_AMOUNT = "{COMBO_SAVING_AMOUNT}";
	public static final String CHANGE_DATE = "{CHANGE_DATE}";
	public static final String ROOM_CHANGE_DATE_KEY = "ROOM_CHANGE_DATE_TEXT";
	public static final String TOTAL_BOOKINGS = "{TOTAL_BOOKINGS}";
	public static final String EFFECTIVE_PRICE = "EFFECTIVE_PRICE"; // myPartner tier specific effective price
	public static final String MY_PARTNER_HERO_EFFECTIVE = "MY_PARTNER_HERO_EFFECTIVE";

	public static final String SORT_CRITERIA_ACCESS_POINT_FIELD_VALUE = "accessPointDrivingDistanceSort|{poi_id}";
	public static final String LBI = "LBI";
	public static final String POPULAR = "POPULAR";
	public static final String MULTI_ROOM_STAY_CHECKIN_TEXT = "Check-in";
	public static final String MULTI_ROOM_STAY_CHECKOUT_TEXT = "Check-out";
	public static final String MULTI_ROOM_STAY_ROOM_CHANGE_TEXT = "Room Change";
	public static final Map<String, String> MULTI_ROOM_STAY_STATES = new HashMap<String, String>() {
		{
			put("INACTIVE", "inactive");
			put("ACTIVE", "active");
		}
	};
	public static final String MULTI_ROOM_STAY_CURRENCY_SYMBOL = "{CURRENCY_SYMBOL}";

	//DEAL Benefits
	public static final String ONLY_TODAY_DEAL_BENEFITS = "ONLY_TODAY_DEAL_BENEFITS";
	public static final String MMT_EXCLUSIVE_BENEFITS = "MMT_EXCLUSIVE_BENEFITS";
	public static final String BLACK_BENEFITS = "BLACK_BENEFITS";
	public static final String LONG_STAY_BENEFITS = "LONG_STAY_BENEFITS";
	public static final String LON_STAY_PERSUASIONS = "LON_STAY_PERSUASIONS";
	public static final String PROFILE_DEV = "dev";
	public static final String DEFAULT_DATE_TEXT = "DEFAULT_DATE_TEXT";

	public static final String ORG_TYPE = "orgType";

	public static final String SPECIAL_REQ_V2_OTHER_REQUEST_TEMPLATE_ID = "1000";
	public static final String SELECTION_TYPE_INPUT_BOX = "INPUTBOX";
	public static final String SELECTION_TYPE_DROP_DOWN = "DROPDOWN";
	public static final String SPECIAL_REQ_V2_INPUT_BOX_PLACEHOLDER_TEXT = "Want to tell us more?";
	public static final String ATTACHED_BATHROOM_TYPE = "attached";
	public static final String SUPER_PREMIUM_EXPERIENCE = "SPE";

	public static final String INCLUSION_TYPE_EXPERIENCES = "PREMIUM_EXPERIENCE";

}
